<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for lib/</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../prettify.css">
    <link rel="stylesheet" href="../base.css">
    <style type='text/css'>
        div.coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class="header high">
    <h1>Code coverage report for <span class="entity">lib/</span></h1>
    <h2>
        Statements: <span class="metric">98.01% <small>(148 / 151)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Branches: <span class="metric">92.94% <small>(79 / 85)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Functions: <span class="metric">100% <small>(20 / 20)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Lines: <span class="metric">99.31% <small>(143 / 144)</small></span> &nbsp;&nbsp;&nbsp;&nbsp;
        Ignored: <span class="metric"><span class="ignore-none">none</span></span> &nbsp;&nbsp;&nbsp;&nbsp;
    </h2>
    <div class="path"><a href="../index.html">All files</a> &#187; lib/</div>
</div>
<div class="body">
<div class="coverage-summary">
<table>
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="constants.js"><a href="constants.js.html">constants.js</a></td>
	<td data-value="100" class="pic high"><span class="cover-fill cover-full" style="width: 100px;"></span><span class="cover-empty" style="width:0px;"></span></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">(1&nbsp;/&nbsp;1)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">(0&nbsp;/&nbsp;0)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">(0&nbsp;/&nbsp;0)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">(1&nbsp;/&nbsp;1)</td>
	</tr>

<tr>
	<td class="file high" data-value="readable_streambuffer.js"><a href="readable_streambuffer.js.html">readable_streambuffer.js</a></td>
	<td data-value="98.82" class="pic high"><span class="cover-fill" style="width: 98px;"></span><span class="cover-empty" style="width:2px;"></span></td>
	<td data-value="98.82" class="pct high">98.82%</td>
	<td data-value="85" class="abs high">(84&nbsp;/&nbsp;85)</td>
	<td data-value="94.34" class="pct high">94.34%</td>
	<td data-value="53" class="abs high">(50&nbsp;/&nbsp;53)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">(11&nbsp;/&nbsp;11)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="83" class="abs high">(83&nbsp;/&nbsp;83)</td>
	</tr>

<tr>
	<td class="file high" data-value="streambuffer.js"><a href="streambuffer.js.html">streambuffer.js</a></td>
	<td data-value="100" class="pic high"><span class="cover-fill cover-full" style="width: 100px;"></span><span class="cover-empty" style="width:0px;"></span></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">(3&nbsp;/&nbsp;3)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">(0&nbsp;/&nbsp;0)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">(0&nbsp;/&nbsp;0)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">(3&nbsp;/&nbsp;3)</td>
	</tr>

<tr>
	<td class="file high" data-value="writable_streambuffer.js"><a href="writable_streambuffer.js.html">writable_streambuffer.js</a></td>
	<td data-value="96.77" class="pic high"><span class="cover-fill" style="width: 96px;"></span><span class="cover-empty" style="width:4px;"></span></td>
	<td data-value="96.77" class="pct high">96.77%</td>
	<td data-value="62" class="abs high">(60&nbsp;/&nbsp;62)</td>
	<td data-value="90.63" class="pct high">90.63%</td>
	<td data-value="32" class="abs high">(29&nbsp;/&nbsp;32)</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">(9&nbsp;/&nbsp;9)</td>
	<td data-value="98.25" class="pct high">98.25%</td>
	<td data-value="57" class="abs high">(56&nbsp;/&nbsp;57)</td>
	</tr>

</tbody>
</table>
</div>
</div>
<div class="footer">
    <div class="meta">Generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Wed Jul 01 2015 04:16:19 GMT+0000 (UTC)</div>
</div>
<script src="../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../sorter.js"></script>
</body>
</html>
