{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/typed-routes/index.ts"], "names": [], "mappings": ";;;;;;AAuBA,0CA0CC;AAjED,yDAAiE;AACjE,sDAAyB;AACzB,0DAA6B;AAE7B,yCAA2D;AAC3D,0CAA2C;AAC3C,2GAEqD;AAErD,MAAM,UAAU,GAAG,IAAA,kCAAc,EAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,EAAE,oCAAsB,CAAC,CAAC;AAIlG;;;GAGG;AACU,QAAA,OAAO,GAAG,EAAE,CAAC;AAE1B;;GAEG;AACH,SAAgB,eAAe,CAC7B,SAAiB,EACjB,EAAE,GAAG,GAAG,UAAU,EAAE,YAAY,GAAG,8BAAsB,EAAE,GAAG,EAAE,CAAC,sBAAsB;;IAEvF,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,uBAAY,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE1E,OAAO,KAAK,UAAU,QAAQ,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAsC;QACnF,qDAAqD;QACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB;YAAE,OAAO;QAE9C,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAC7B,IAAI,YAAY,GAAG,mBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC7E,MAAM,eAAe,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,mBAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAE7C,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,yEAAyE;QACzE,YAAY,GAAG,KAAK,YAAY,EAAE,CAAC;QAEnC,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC3B,IAAI,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;gBACjC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBAChC,gBAAgB,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YAC1B,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACxB,IAAI,IAAA,uBAAY,EAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3B,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAC7B,gBAAgB,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gBAAgB,GAAG,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACrB,4GAA4G;YAC5G,4EAA4E;YAC5E,YAAY,CAAC,SAAS,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;GAWG;AACU,QAAA,sBAAsB,GAAG,QAAQ,CAC5C,CACE,SAAiB,EACjB,UAA4C,EAAE,EAC9C,MAA8B,UAAU,EACxC,EAAE;IACF,6FAA6F;IAC7F,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,IAAA,wCAA6B,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,iBAAE,CAAC,aAAa,CAAC,mBAAI,CAAC,OAAO,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,CAAC;IACnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;;GAGG;AACH,SAAS,QAAQ,CACf,EAAK,EACL,UAAkB,IAAI;IAEtB,IAAI,KAAoC,CAAC;IACzC,OAAO,CAAC,GAAG,IAAI,EAAE,EAAE;QACjB,YAAY,CAAC,KAAK,CAAC,CAAC;QACpB,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YACtB,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { EXPO_ROUTER_CTX_IGNORE } from 'expo-router/_ctx-shared';\nimport fs from 'node:fs';\nimport path from 'node:path';\n\nimport { getTypedRoutesDeclarationFile } from './generate';\nimport { isTypedRoute } from '../matchers';\nimport requireContext, {\n  RequireContextPonyFill,\n} from '../testing-library/require-context-ponyfill';\n\nconst defaultCtx = requireContext(process.env.EXPO_ROUTER_APP_ROOT, true, EXPO_ROUTER_CTX_IGNORE);\n\nexport type { RequireContextPonyFill } from '../testing-library/require-context-ponyfill';\n\n/**\n * This file is imported via `@expo/cli`. While users should be using the same SDK version of `expo-router` as `@expo/cli`,\n * this export allows us to ensure that the version of the `expo-router` package is compatible with the version of `@expo/cli`.\n */\nexport const version = 52;\n\n/**\n * Generate a Metro watch handler that regenerates the typed routes declaration file\n */\nexport function getWatchHandler(\n  outputDir: string,\n  { ctx = defaultCtx, regenerateFn = regenerateDeclarations } = {} // Exposed for testing\n) {\n  const routeFiles = new Set(ctx.keys().filter((key) => isTypedRoute(key)));\n\n  return async function callback({ filePath, type }: { filePath: string; type: string }) {\n    // Sanity check that we are in an Expo Router project\n    if (!process.env.EXPO_ROUTER_APP_ROOT) return;\n\n    let shouldRegenerate = false;\n    let relativePath = path.relative(process.env.EXPO_ROUTER_APP_ROOT, filePath);\n    const isInsideAppRoot = !relativePath.startsWith('../');\n    const basename = path.basename(relativePath);\n\n    if (!isInsideAppRoot) return;\n\n    // require.context paths always start with './' when relative to the root\n    relativePath = `./${relativePath}`;\n\n    if (type === 'delete') {\n      ctx.__delete(relativePath);\n      if (routeFiles.has(relativePath)) {\n        routeFiles.delete(relativePath);\n        shouldRegenerate = true;\n      }\n    } else if (type === 'add') {\n      ctx.__add(relativePath);\n      if (isTypedRoute(basename)) {\n        routeFiles.add(relativePath);\n        shouldRegenerate = true;\n      }\n    } else {\n      shouldRegenerate = routeFiles.has(relativePath);\n    }\n\n    if (shouldRegenerate) {\n      // TODO(@kitten): This was altered from `regenerateFn(outputDir, ctx)` which, as per the types, is incorrect\n      // It's unclear whether fixing this will have other unintended consequences!\n      regenerateFn(outputDir, {}, ctx);\n    }\n  };\n}\n\n/**\n * Regenerate the declaration file.\n *\n * This function needs to be debounced due to Metro's handling of renaming folders.\n * For example, if you have the file /(tabs)/route.tsx and you rename the folder to /(tabs,test)/route.tsx\n *\n * Metro will fire 2 filesystem events:\n *  - ADD /(tabs,test)/router.tsx\n *  - DELETE /(tabs)/router.tsx\n *\n * If you process the types after the ADD, then they will crash as you will have conflicting routes\n */\nexport const regenerateDeclarations = debounce(\n  (\n    outputDir: string,\n    options: { partialTypedGroups?: boolean } = {},\n    ctx: RequireContextPonyFill = defaultCtx\n  ) => {\n    // Don't crash the process, just log the error. The user will most likely fix it and continue\n    try {\n      const file = getTypedRoutesDeclarationFile(ctx, options);\n      if (!file) return;\n      fs.writeFileSync(path.resolve(outputDir, './router.d.ts'), file);\n    } catch (error) {\n      console.error(error);\n    }\n  }\n);\n\n/**\n * Debounce a function to only run once after a period of inactivity\n * If called while waiting, it will reset the timer\n */\nfunction debounce<T extends (...args: any[]) => any>(\n  fn: T,\n  timeout: number = 1000\n): (...args: Parameters<T>) => void {\n  let timer: ReturnType<typeof setTimeout>;\n  return (...args) => {\n    clearTimeout(timer);\n    timer = setTimeout(() => {\n      fn(...args);\n    }, timeout);\n  };\n}\n"]}