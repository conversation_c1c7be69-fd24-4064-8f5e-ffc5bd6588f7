{"version": 3, "file": "TabSlot.js", "sourceRoot": "", "sources": ["../../src/ui/TabSlot.tsx"], "names": [], "mappings": ";;AAqDA,gCAkCC;AAiBD,0BAEC;AAKD,sDAyBC;AAKD,8BAEC;AA/ID,iCAA+D;AAC/D,+CAAoD;AACpD,+DAA+D;AAE/D,6CAA0D;AAE1D,kDAAyD;AAmCzD;;;;;;;;;;;GAWG;AACH,SAAgB,UAAU,CAAC,EACzB,qBAAqB,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,uBAAQ,CAAC,EAAE,CAAC,EACvE,KAAK,EACL,QAAQ,GAAG,qBAAqB,MAChB,EAAE;IAClB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,IAAA,+BAAmB,GAAE,CAAC;IACrD,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;IACtD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAA,gBAAQ,EAAC,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;QAC7B,SAAS,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,OAAO,CACL,CAAC,sCAAe,CACd,OAAO,CAAC,CAAC,qBAAqB,CAAC,CAC/B,YAAY,CACZ,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CACvC;MAAA,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACjC,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAA8B,CAAC;YAEvE,OAAO,CACL,CAAC,uBAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CACxE;YAAA,CAAC,QAAQ,CAAC,UAAU,EAAE;oBACpB,KAAK;oBACL,SAAS,EAAE,KAAK,CAAC,KAAK,KAAK,KAAK;oBAChC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;oBACzB,qBAAqB;iBACtB,CAAC,CACJ;UAAA,EAAE,uBAAU,CAAC,QAAQ,CAAC,CACvB,CAAC;QACJ,CAAC,CAAC,CACJ;IAAA,EAAE,sCAAe,CAAC,CACnB,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAgB,OAAO,CAAC,KAAmB;IACzC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CACnC,UAA0B,EAC1B,EAAE,SAAS,EAAE,MAAM,EAAE,qBAAqB,EAAyB;IAEnE,MAAM,EAAE,IAAI,GAAG,IAAI,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC;IAExE,IAAI,aAAa,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QAClC,4DAA4D;QAC5D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,CAAC,6BAAM,CACL,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAC1B,OAAO,CAAC,CAAC,qBAAqB,CAAC,CAC/B,aAAa,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjC,YAAY,CAAC,CAAC,YAAY,CAAC,CAC3B,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CACtE;MAAA,CAAC,UAAU,CAAC,MAAM,EAAE,CACtB;IAAA,EAAE,6BAAM,CAAC,CACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,SAAS,CAAC,KAAwB;IAChD,OAAO,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC;AAChC,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,MAAM,EAAE;QACN,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,MAAM;KACf;IACD,eAAe,EAAE;QACf,UAAU,EAAE,CAAC;QACb,QAAQ,EAAE,CAAC;KACZ;IACD,OAAO,EAAE;QACP,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,MAAM;QACf,UAAU,EAAE,CAAC;QACb,QAAQ,EAAE,CAAC;KACZ;IACD,SAAS,EAAE;QACT,MAAM,EAAE,CAAC,CAAC;QACV,OAAO,EAAE,MAAM;QACf,UAAU,EAAE,CAAC;QACb,QAAQ,EAAE,CAAC;KACZ;CACF,CAAC,CAAC", "sourcesContent": ["import { ComponentProps, ReactElement, useState } from 'react';\nimport { Platform, StyleSheet } from 'react-native';\nimport { ScreenContainer, Screen } from 'react-native-screens';\n\nimport { TabContext, TabsDescriptor } from './TabContext';\nimport { TabListProps } from './TabList';\nimport { useNavigatorContext } from '../views/Navigator';\n\nexport type TabSlotProps = ComponentProps<typeof ScreenContainer> & {\n  /**\n   * Remove inactive screens.\n   */\n  detachInactiveScreens?: boolean;\n  /**\n   * Override how the `Screen` component is rendered.\n   */\n  renderFn?: typeof defaultTabsSlotRender;\n};\n\n/**\n * Options provided to the `UseTabSlotOptions`.\n */\nexport type TabsSlotRenderOptions = {\n  /**\n   * Index of screen.\n   */\n  index: number;\n  /**\n   * Whether the screen is focused.\n   */\n  isFocused: boolean;\n  /**\n   * Whether the screen has been loaded.\n   */\n  loaded: boolean;\n  /**\n   * Should the screen be unloaded when inactive.\n   */\n  detachInactiveScreens: boolean;\n};\n\n/**\n * Returns a `ReactElement` of the current tab.\n *\n * @example\n * ```tsx\n * function MyTabSlot() {\n *   const slot = useTabSlot();\n *\n *   return slot;\n * }\n * ```\n */\nexport function useTabSlot({\n  detachInactiveScreens = ['android', 'ios', 'web'].includes(Platform.OS),\n  style,\n  renderFn = defaultTabsSlotRender,\n}: TabSlotProps = {}) {\n  const { state, descriptors } = useNavigatorContext();\n  const focusedRouteKey = state.routes[state.index].key;\n  const [loaded, setLoaded] = useState({ [focusedRouteKey]: true });\n\n  if (!loaded[focusedRouteKey]) {\n    setLoaded({ ...loaded, [focusedRouteKey]: true });\n  }\n\n  return (\n    <ScreenContainer\n      enabled={detachInactiveScreens}\n      hasTwoStates\n      style={[styles.screenContainer, style]}>\n      {state.routes.map((route, index) => {\n        const descriptor = descriptors[route.key] as unknown as TabsDescriptor;\n\n        return (\n          <TabContext.Provider key={descriptor.route.key} value={descriptor.options}>\n            {renderFn(descriptor, {\n              index,\n              isFocused: state.index === index,\n              loaded: loaded[route.key],\n              detachInactiveScreens,\n            })}\n          </TabContext.Provider>\n        );\n      })}\n    </ScreenContainer>\n  );\n}\n\n/**\n * Renders the current tab.\n *\n * @see [`useTabSlot`](#usetabslot) for a hook version of this component.\n *\n * @example\n * ```tsx\n * <Tabs>\n *  <TabSlot />\n *  <TabList>\n *   <TabTrigger name=\"home\" href=\"/\" />\n *  </TabList>\n * </Tabs>\n * ```\n */\nexport function TabSlot(props: TabSlotProps) {\n  return useTabSlot(props);\n}\n\n/**\n * @hidden\n */\nexport function defaultTabsSlotRender(\n  descriptor: TabsDescriptor,\n  { isFocused, loaded, detachInactiveScreens }: TabsSlotRenderOptions\n) {\n  const { lazy = true, unmountOnBlur, freezeOnBlur } = descriptor.options;\n\n  if (unmountOnBlur && !isFocused) {\n    return null;\n  }\n\n  if (lazy && !loaded && !isFocused) {\n    // Don't render a lazy screen if we've never navigated to it\n    return null;\n  }\n\n  return (\n    <Screen\n      key={descriptor.route.key}\n      enabled={detachInactiveScreens}\n      activityState={isFocused ? 2 : 0}\n      freezeOnBlur={freezeOnBlur}\n      style={[styles.screen, isFocused ? styles.focused : styles.unfocused]}>\n      {descriptor.render()}\n    </Screen>\n  );\n}\n\n/**\n * @hidden\n */\nexport function isTabSlot(child: ReactElement<any>): child is ReactElement<TabListProps> {\n  return child.type === TabSlot;\n}\n\nconst styles = StyleSheet.create({\n  screen: {\n    flex: 1,\n    position: 'relative',\n    height: '100%',\n  },\n  screenContainer: {\n    flexShrink: 0,\n    flexGrow: 1,\n  },\n  focused: {\n    zIndex: 1,\n    display: 'flex',\n    flexShrink: 0,\n    flexGrow: 1,\n  },\n  unfocused: {\n    zIndex: -1,\n    display: 'none',\n    flexShrink: 1,\n    flexGrow: 0,\n  },\n});\n"]}