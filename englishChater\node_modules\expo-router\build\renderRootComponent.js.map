{"version": 3, "file": "renderRootComponent.js", "sourceRoot": "", "sources": ["../src/renderRootComponent.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,kDAwCC;AA5FD,+BAA6C;AAC7C,6CAA+B;AAC/B,+CAAoC;AAEpC,6DAA+C;AAE/C,SAAS,YAAY,CAAC,GAAQ;IAC5B,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,iBAAiB,EAAE,CAAC;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IACzC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,KAAK,MAAM,CAAC,SAAS,CAAC;AACpC,CAAC;AAED,SAAS,aAAa,CAAC,KAAU;IAC/B,OAAO,CACL,KAAK;QACL,OAAO,KAAK,KAAK,QAAQ;QACzB,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;QAC9B,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,CAClC,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY,CAAC,KAAU;IAC9B,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,OAAO,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,CAAC;AAED;;;GAGG;AACH,SAAgB,mBAAmB,CAAC,SAAmC;IACrE,IAAI,CAAC;QACH,kEAAkE;QAClE,UAAU,CAAC,GAAG,EAAE;YACd,YAAY,CAAC,8BAA8B,EAAE,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE;YACzB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC1C,MAAM,EAAE,gBAAgB,EAAE,GACxB,OAAO,CAAC,mCAAmC,CAAuD,CAAC;gBACrG,IAAA,4BAAqB,EAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,IAAA,4BAAqB,EAAC,SAAS,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,uEAAuE;QACvE,YAAY,CAAC,SAAS,EAAE,CAAC;QAEzB,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9B,2CAA2C;QAC3C,mFAAmF;QACnF,yIAAyI;QACzI,+FAA+F;QAC/F,IAAA,4BAAqB,EAAC,GAAG,EAAE,CAAC,CAAC,mBAAI,CAAC,AAAD,EAAG,CAAC,CAAC;QAEtC,gFAAgF;QAChF,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YAClC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrB,OAAO,CAAC,KAAK,CAAC,kEAAkE,CAAC,CAAC;QACpF,CAAC;QAED,+CAA+C;QAC/C,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,+CAA+C;IACjD,CAAC;AACH,CAAC", "sourcesContent": ["import { registerRootComponent } from 'expo';\nimport * as React from 'react';\nimport { View } from 'react-native';\n\nimport * as SplashScreen from './utils/splash';\n\nfunction isBaseObject(obj: any) {\n  if (Object.prototype.toString.call(obj) !== '[object Object]') {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(obj);\n  if (proto === null) {\n    return true;\n  }\n  return proto === Object.prototype;\n}\n\nfunction isErrorShaped(error: any): error is Error {\n  return (\n    error &&\n    typeof error === 'object' &&\n    typeof error.name === 'string' &&\n    typeof error.message === 'string'\n  );\n}\n\n/**\n * After we throw this error, any number of tools could handle it.\n * This check ensures the error is always in a reason state before surfacing it to the runtime.\n */\nfunction convertError(error: any) {\n  if (isErrorShaped(error)) {\n    return error;\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    if (error == null) {\n      return new Error('A null/undefined error was thrown.');\n    }\n  }\n\n  if (isBaseObject(error)) {\n    return new Error(JSON.stringify(error));\n  }\n\n  return new Error(String(error));\n}\n\n/**\n * Register and mount the root component using the predefined rendering\n * method. This function ensures the Splash Screen and errors are handled correctly.\n */\nexport function renderRootComponent(Component: React.ComponentType<any>) {\n  try {\n    // This must be delayed so the user has a chance to call it first.\n    setTimeout(() => {\n      SplashScreen._internal_preventAutoHideAsync?.();\n    });\n\n    React.startTransition(() => {\n      if (process.env.NODE_ENV !== 'production') {\n        const { withErrorOverlay } =\n          require('@expo/metro-runtime/error-overlay') as typeof import('@expo/metro-runtime/error-overlay');\n        registerRootComponent(withErrorOverlay(Component));\n      } else {\n        registerRootComponent(Component);\n      }\n    });\n  } catch (e) {\n    // Hide the splash screen if there was an error so the user can see it.\n    SplashScreen.hideAsync();\n\n    const error = convertError(e);\n    // Prevent the app from throwing confusing:\n    //  ERROR  Invariant Violation: \"main\" has not been registered. This can happen if:\n    // * Metro (the local dev server) is run from the wrong folder. Check if Metro is running, stop it and restart it in the current project.\n    // * A module failed to load due to an error and `AppRegistry.registerComponent` wasn't called.\n    registerRootComponent(() => <View />);\n\n    // Console is pretty useless on native, on web you get interactive stack traces.\n    if (process.env.EXPO_OS === 'web') {\n      console.error(error);\n      console.error(`A runtime error has occurred while rendering the root component.`);\n    }\n\n    // Give React a tick to render before throwing.\n    setTimeout(() => {\n      throw error;\n    });\n\n    // TODO: Render a production-only error screen.\n  }\n}\n"]}