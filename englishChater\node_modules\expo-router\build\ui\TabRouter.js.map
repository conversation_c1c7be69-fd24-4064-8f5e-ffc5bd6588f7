{"version": 3, "file": "TabRouter.js", "sourceRoot": "", "sources": ["../../src/ui/TabRouter.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,sCAoEC;AArGD,qDAQkC;AAClC,sDAAwC;AAwBxC,SAAgB,aAAa,CAAC,EAAE,UAAU,EAAE,GAAG,OAAO,EAAwB;IAC5E,MAAM,WAAW,GAAG,IAAA,kBAAW,EAAC,OAAO,CAAC,CAAC;IAEzC,MAAM,MAAM,GAGR;QACF,GAAG,WAAW;QACd,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO;YACtC,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC9B,OAAO,WAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;YACjC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YAEjC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,6CAA6C;gBAC7C,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACvC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAEnF,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,iFAAiF;gBACjF,OAAO,IAAI,CAAC;YACd,CAAC;YAED,+DAA+D;YAC/D,IAAI,WAAW,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YAEzF,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;gBACtE,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;oBAC7B,KAAK,OAAO,CAAC,CAAC,CAAC;wBACb,WAAW,GAAG,KAAK,CAAC;wBACpB,MAAM;oBACR,CAAC;oBACD,KAAK,QAAQ,CAAC,CAAC,CAAC;wBACd,WAAW,GAAG,IAAI,CAAC;wBACnB,MAAM;oBACR,CAAC;oBACD,KAAK,SAAS,CAAC,CAAC,CAAC;wBACf,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC;wBAC1D,MAAM;oBACR,CAAC;oBACD,OAAO,CAAC,CAAC,CAAC;wBACR,6DAA6D;wBAC7D,MAAM,CAAC,OAAO,CAAC,KAAqB,CAAC;oBACvC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;oBACnC,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC;oBACrC,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;iBACjC,CAAC;gBACF,OAAO,WAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACN,OAAO,WAAW,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;KACF,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import {\n  CommonNavigationAction,\n  ParamListBase,\n  Tab<PERSON>out<PERSON> as RNTabRouter,\n  Router,\n  TabActionType as RNTabActionType,\n  TabNavigationState,\n  TabRouterOptions as RNTabRouterOptions,\n} from '@react-navigation/native';\nimport * as Linking from 'expo-linking';\n\nimport { TriggerMap } from './common';\n\nexport type ExpoTabRouterOptions = RNTabRouterOptions & {\n  triggerMap: TriggerMap;\n};\n\nexport type ExpoTabsResetValue = 'always' | 'onFocus' | 'never';\n\nexport type ExpoTabActionType =\n  | RNTabActionType\n  | CommonNavigationAction\n  | {\n      type: 'JUMP_TO';\n      source?: string;\n      target?: string;\n      payload: {\n        name: string;\n        reset?: ExpoTabsResetValue;\n        params?: object;\n      };\n    };\n\nexport function ExpoTabRouter({ triggerMap, ...options }: ExpoTabRouterOptions) {\n  const rnTabRouter = RNTabRouter(options);\n\n  const router: Router<\n    TabNavigationState<ParamListBase>,\n    ExpoTabActionType | CommonNavigationAction\n  > = {\n    ...rnTabRouter,\n    getStateForAction(state, action, options) {\n      if (action.type !== 'JUMP_TO') {\n        return rnTabRouter.getStateForAction(state, action, options);\n      }\n\n      const name = action.payload.name;\n      const trigger = triggerMap[name];\n\n      if (!trigger) {\n        // This is probably for a different navigator\n        return null;\n      } else if (trigger.type === 'external') {\n        Linking.openURL(trigger.href);\n        return state;\n      }\n\n      const route = state.routes.find((route) => route.name === trigger.routeNode.route);\n\n      if (!route) {\n        // This shouldn't occur, but lets just hand it off to the next navigator in case.\n        return null;\n      }\n\n      // We should reset if this is the first time visiting the route\n      let shouldReset = !state.history.some((item) => item.key === route?.key) && !route.state;\n\n      if (!shouldReset && 'reset' in action.payload && action.payload.reset) {\n        switch (action.payload.reset) {\n          case 'never': {\n            shouldReset = false;\n            break;\n          }\n          case 'always': {\n            shouldReset = true;\n            break;\n          }\n          case 'onFocus': {\n            shouldReset = state.routes[state.index].key === route.key;\n            break;\n          }\n          default: {\n            // TypeScript trick to ensure all use-cases are accounted for\n            action.payload.reset satisfies never;\n          }\n        }\n      }\n\n      if (shouldReset) {\n        options.routeParamList[route.name] = {\n          ...options.routeParamList[route.name],\n          ...trigger.action.payload.params,\n        };\n        return rnTabRouter.getStateForAction(state, trigger.action, options);\n      } else {\n        return rnTabRouter.getStateForRouteFocus(state, route.key);\n      }\n    },\n  };\n\n  return router;\n}\n"]}