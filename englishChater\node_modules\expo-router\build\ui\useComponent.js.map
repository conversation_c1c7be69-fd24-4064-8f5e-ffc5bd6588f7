{"version": 3, "file": "useComponent.js", "sourceRoot": "", "sources": ["../../src/ui/useComponent.tsx"], "names": [], "mappings": ";;AAgBA,oCAyBC;AAzCD,iCAA2D;AAY3D,MAAM,iBAAiB,GAAG,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAS,EAAE,EAAE;IACxD,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC1B,CAAC,CAAC;AAEF,SAAgB,YAAY,CAAC,MAAc;IACzC,MAAM,SAAS,GAAG,IAAA,cAAM,EAAgB,MAAM,CAAC,CAAC;IAEhD,+CAA+C;IAC/C,mDAAmD;IACnD,yCAAyC;IACzC,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC;IAE3B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,OAAO,IAAA,cAAM,EACX,IAAA,kBAAU,EAAC,CAAC,EAAE,QAAQ,EAAiC,EAAE,IAAI,EAAE,EAAE;QAC/D,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC;QAEjC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CACb,+EAA+E,CAChF,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC3E,CAAC,CAAC,CACH,CAAC,OAAO,CAAC;AACZ,CAAC", "sourcesContent": ["import { useRef, forwardRef, useEffect, JSX } from 'react';\n\n/**\n * Copied from @react-navigation/core\n */\ntype Render = (children: React.ReactNode) => JSX.Element;\n\ntype Props = {\n  render: Render;\n  children: React.ReactNode;\n};\n\nconst NavigationContent = ({ render, children }: Props) => {\n  return render(children);\n};\n\nexport function useComponent(render: Render) {\n  const renderRef = useRef<Render | null>(render);\n\n  // Normally refs shouldn't be mutated in render\n  // But we return a component which will be rendered\n  // So it's just for immediate consumption\n  renderRef.current = render;\n\n  useEffect(() => {\n    renderRef.current = null;\n  });\n\n  return useRef(\n    forwardRef(({ children }: { children: React.ReactNode }, _ref) => {\n      const render = renderRef.current;\n\n      if (render === null) {\n        throw new Error(\n          'The returned component must be rendered in the same render phase as the hook.'\n        );\n      }\n\n      return <NavigationContent render={render}>{children}</NavigationContent>;\n    })\n  ).current;\n}\n"]}