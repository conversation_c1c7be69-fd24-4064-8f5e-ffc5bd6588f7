{"version": 3, "file": "statusbar.js", "sourceRoot": "", "sources": ["../../src/utils/statusbar.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAAuC;AACvC,+CAAwC;AACxC,+EAA4D;AAE5D,MAAM,yCAAyC,GAC7C,uBAAQ,CAAC,EAAE,KAAK,KAAK;IACrB,CAAC,CAAC,wBAAS,CAAC,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE,wCAAwC,CAAC;AAEtE,QAAA,4BAA4B,GACvC,CAAC,IAAA,2CAAY,GAAE,IAAI,CAAC,yCAAyC,CAAC", "sourcesContent": ["import Constants from 'expo-constants';\nimport { Platform } from 'react-native';\nimport { isEdgeToEdge } from 'react-native-is-edge-to-edge';\n\nconst hasViewControllerBasedStatusBarAppearance =\n  Platform.OS === 'ios' &&\n  !!Constants.expoConfig?.ios?.infoPlist?.UIViewControllerBasedStatusBarAppearance;\n\nexport const canOverrideStatusBarBehavior =\n  !isEdgeToEdge() && !hasViewControllerBasedStatusBarAppearance;\n"]}