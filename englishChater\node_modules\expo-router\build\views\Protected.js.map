{"version": 3, "file": "Protected.js", "sourceRoot": "", "sources": ["../../src/views/Protected.tsx"], "names": [], "mappings": ";;;AAQA,0DAIC;AAZD,iCAAmF;AAEnF,8CAAsC;AAIzB,QAAA,SAAS,GAAG,kBAA0C,CAAC;AAEpE,SAAgB,uBAAuB,CAAC,KAAgB;IACtD,OAAO,OAAO,CACZ,IAAA,sBAAc,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAK,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,IAAK,KAAK,CAAC,KAAa,CAChG,CAAC;AACJ,CAAC", "sourcesContent": ["import { FunctionComponent, isValidElement, ReactElement, ReactNode } from 'react';\n\nimport { Group } from '../primitives';\n\nexport type ProtectedProps = { guard: boolean; children?: ReactNode };\n\nexport const Protected = Group as FunctionComponent<ProtectedProps>;\n\nexport function isProtectedReactElement(child: ReactNode): child is ReactElement<ProtectedProps> {\n  return Boolean(\n    isValidElement(child) && child.type === Group && child.props && 'guard' in (child.props as any)\n  );\n}\n"]}