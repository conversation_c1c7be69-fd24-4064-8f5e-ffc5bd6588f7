{"version": 3, "file": "href.js", "sourceRoot": "", "sources": ["../../src/link/href.ts"], "names": [], "mappings": ";;;AAoBA,sEAsCC;AAtDD,oEAAoE;AAC7D,MAAM,WAAW,GAAG,CAAC,IAAU,EAAU,EAAE;IAChD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,OAAO,IAAA,mBAAW,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;IACD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;IACjC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,uBAAuB,CAAC,IAAI,EAAE;QACzD,GAAG,IAAI,CAAC,MAAM;KACf,CAAC,CAAC;IACH,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAC/C,OAAO,QAAQ,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7D,CAAC,CAAC;AAbW,QAAA,WAAW,eAatB;AAEF,SAAgB,6BAA6B,CAC3C,IAAY,EACZ,EAAE,QAAQ,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,KAAyB,EAAE,EACvD,EAAE,mBAAmB,KAAoB,EAAE;IAE3C,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACzB,oEAAoE;QACpE,IAAI,IAAI,GACN,QAAQ;YACN,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;gBAAE,OAAO,OAAO,CAAC;YAE7C,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACN,OAAO,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC5C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC/B,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;aACD,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;QAEtB,IAAI,mBAAmB,EAAE,CAAC;YACxB,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,mBAAmB,IAAI,EAAE,CAAC,CAAC;QAErD,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;IACxC,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,uBAAuB,CAC9B,QAAgB,EAChB,MAA2B;IAE3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QACvD,MAAM,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC;QAC9B,MAAM,cAAc,GAAG,OAAO,GAAG,GAAG,CAAC;QACrC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAC7C,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,SAAS;QACX,CAAC;QAED,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;IACrB,CAAC;IACD,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,WAAW,CAAC,KAAU;IAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,OAAO,kBAAkB,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,iBAAiB,CAAC,MAA2B;IACpD,OAAO,CACL,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;QACpB,uBAAuB;SACtB,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC;SACpC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,kBAAkB,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;SACvE,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;AACJ,CAAC", "sourcesContent": ["import { UrlObject } from '../LocationProvider';\nimport { LinkToOptions } from '../global-state/routing';\nimport { Href } from '../types';\n\n/** Resolve an href object into a fully qualified, relative href. */\nexport const resolveHref = (href: Href): string => {\n  if (typeof href === 'string') {\n    return resolveHref({ pathname: href });\n  }\n  const path = href.pathname ?? '';\n  if (!href?.params) {\n    return path;\n  }\n  const { pathname, params } = createQualifiedPathname(path, {\n    ...href.params,\n  });\n  const paramsString = createQueryParams(params);\n  return pathname + (paramsString ? `?${paramsString}` : '');\n};\n\nexport function resolveHrefStringWithSegments(\n  href: string,\n  { segments = [], params = {} }: Partial<UrlObject> = {},\n  { relativeToDirectory }: LinkToOptions = {}\n) {\n  if (href.startsWith('.')) {\n    // Resolve base path by merging the current segments with the params\n    let base =\n      segments\n        ?.map((segment) => {\n          if (!segment.startsWith('[')) return segment;\n\n          if (segment.startsWith('[...')) {\n            segment = segment.slice(4, -1);\n            const param = params[segment];\n            if (Array.isArray(param)) {\n              return param.join('/');\n            } else {\n              return param?.split(',')?.join('/') ?? '';\n            }\n          } else {\n            segment = segment.slice(1, -1);\n            return params[segment];\n          }\n        })\n        .filter(Boolean)\n        .join('/') ?? '/';\n\n    if (relativeToDirectory) {\n      base = `${base}/`;\n    }\n\n    const url = new URL(href, `http://hostname/${base}`);\n\n    href = `${url.pathname}${url.search}`;\n  }\n\n  return href;\n}\n\nfunction createQualifiedPathname(\n  pathname: string,\n  params: Record<string, any>\n): { pathname: string; params: any } {\n  for (const [key, value = ''] of Object.entries(params)) {\n    const dynamicKey = `[${key}]`;\n    const deepDynamicKey = `[...${key}]`;\n    if (pathname.includes(dynamicKey)) {\n      pathname = pathname.replace(dynamicKey, encodeParam(value));\n    } else if (pathname.includes(deepDynamicKey)) {\n      pathname = pathname.replace(deepDynamicKey, encodeParam(value));\n    } else {\n      continue;\n    }\n\n    delete params[key];\n  }\n  return { pathname, params };\n}\n\nfunction encodeParam(param: any): string {\n  if (Array.isArray(param)) {\n    return param.map((p) => encodeParam(p)).join('/');\n  }\n\n  return encodeURIComponent(param.toString());\n}\n\nfunction createQueryParams(params: Record<string, any>): string {\n  return (\n    Object.entries(params)\n      // Allow nullish params\n      .filter(([, value]) => value != null)\n      .map(([key, value]) => `${key}=${encodeURIComponent(value.toString())}`)\n      .join('&')\n  );\n}\n"]}