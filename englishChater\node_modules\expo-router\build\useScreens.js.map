{"version": 3, "file": "useScreens.js", "sourceRoot": "", "sources": ["../src/useScreens.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;AAmJb,4CAkBC;AAmDD,gEAgEC;AAED,oDAwBC;AAED,sCAcC;AAED,sCAgBC;AAlVD,qDAQkC;AAClC,kDAA0B;AAE1B,mCAA6F;AAC7F,8DAAiE;AACjE,gEAAoD;AACpD,6CAAsC;AAEtC,mDAAgD;AAChD,+DAA4D;AAC5D,qCAAkC;AAmClC,SAAS,iBAAiB,CACxB,QAAqB,EACrB,QAAuB,EAAE,EACzB,gBAAyB;IAEzB,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;QACnB,OAAO,QAAQ;aACZ,IAAI,CAAC,IAAA,6BAAqB,EAAC,gBAAgB,CAAC,CAAC;aAC7C,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,MAAM,OAAO,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;IAE9B,MAAM,OAAO,GAAG,KAAK;SAClB,GAAG,CACF,CAAC,EACC,IAAI,EACJ,QAAQ,EACR,aAAa,EACb,SAAS,EACT,OAAO,EACP,KAAK,EACL,mBAAmB,EAAE,QAAQ,GAC9B,EAAE,EAAE;QACH,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CACV,uDAAuD,IAAI,kBAAkB,CAC9E,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC;QACtE,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,CACV,sCAAsC,IAAI,8BAA8B,EACxE,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CACnC,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,CAAC;YACN,oCAAoC;YACpC,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YAClC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAE9B,qDAAqD;YACrD,IAAI,QAAQ,EAAE,CAAC;gBACb,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBACjC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;gBAC3E,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,IAAI,CACV,sCAAsC,IAAI,iEAAiE,CAC5G,CAAC;gBACF,IAAI,QAAQ,EAAE,CAAC;oBACb,OAAO,CAAC,IAAI,CACV,UAAU,IAAI,0DAA0D,CACzE,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,EAAE,CAAC;gBACpB,oDAAoD;gBACpD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBACjC,KAAK,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;gBACzB,CAAC;qBAAM,IAAI,OAAO,QAAQ,KAAK,UAAU,IAAI,IAAI,EAAE,CAAC;oBAClD,KAAK,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;gBAC5D,CAAC;qBAAM,IAAI,QAAQ,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC;oBACrC,KAAK,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;YAED,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC,CACF;SACA,MAAM,CAAC,OAAO,CAGd,CAAC;IAEJ,6BAA6B;IAC7B,OAAO,CAAC,IAAI,CACV,GAAG,OAAO,CAAC,IAAI,CAAC,IAAA,6BAAqB,EAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAChG,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,KAAoB,EACpB,gBAA6B;IAE7B,MAAM,IAAI,GAAG,IAAA,oBAAY,GAAE,CAAC;IAE5B,MAAM,MAAM,GAAG,IAAI,EAAE,QAAQ,EAAE,MAAM;QACnC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC;QAChE,CAAC,CAAC,EAAE,CAAC;IACP,OAAO,eAAK,CAAC,OAAO,CAClB,GAAG,EAAE,CACH,MAAM;SACH,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACzD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACb,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC,EACN,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAC3B,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,KAAgB,EAAE,EAAE,aAAa,EAAE,GAAG,SAAS,EAAe;IAChF,gLAAgL;IAChL,IAAI,SAAS,EAAE,OAAO,IAAI,OAAO,EAAE,CAAC;QAClC,SAAS,CAAC,OAAO,CAAC,WAAW,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC;IAChG,CAAC;IAED,IAAI,aAAa,EAAE,CAAC;QAClB,MAAM,OAAO,GAAG,eAAK,CAAC,UAAU,CAAC,CAAC,KAAU,EAAE,GAAQ,EAAE,EAAE;YACxD,MAAM,QAAQ,GAAG,eAAK,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,IAAI,uBAAU,EAAE;gBACpE,GAAG,KAAK;gBACR,GAAG;aACJ,CAAC,CAAC;YACH,OAAO,CAAC,SAAG,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,SAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,WAAW,GAAG,iBAAiB,KAAK,CAAC,UAAU,GAAG,CAAC;QAC7D,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO;SACjB,CAAC;IACJ,CAAC;IACD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,IACE,OAAO,SAAS,CAAC,OAAO,KAAK,QAAQ;YACrC,SAAS,CAAC,OAAO;YACjB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAC3C,CAAC;YACD,OAAO,EAAE,OAAO,EAAE,uBAAU,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAED,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;AACxC,CAAC;AAED,SAAS,eAAe,CAAC,KAAgB,EAAE,GAAgB;IACzD,IAAI,CAAC,CAAC,GAAG,YAAY,OAAO,CAAC,EAAE,CAAC;QAC9B,OAAO,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AAChD,CAAC;AAED,qDAAqD;AACrD,2DAA2D;AAC3D,MAAM,cAAc,GAAG,IAAI,OAAO,EAAuC,CAAC;AAE1E,mFAAmF;AACnF,SAAgB,0BAA0B,CAAC,KAAgB;IACzD,IAAI,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO,cAAc,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;IACpC,CAAC;IAED,IAAI,eAEwB,CAAC;IAE7B,sEAAsE;IACtE,IAAI,qBAAuB,KAAK,MAAM,EAAE,CAAC;QACvC,eAAe,GAAG,eAAK,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YACtC,MAAM,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;YAC9B,OAAO,eAAe,CAAC,KAAK,EAAE,GAAG,CAE/B,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,eAAe,CAAC,WAAW,GAAG,cAAc,KAAK,CAAC,KAAK,GAAG,CAAC;QAC7D,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9B,eAAe,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAQ,CAAC;IACpD,CAAC;IACD,SAAS,SAAS,CAAC;IACjB,yCAAyC;IACzC,2EAA2E;IAC3E,KAAK,EACL,UAAU;IAEV,wCAAwC;IACxC,GAAG,KAAK,EACJ;QACJ,MAAM,YAAY,GAAG,IAAA,wBAAe,GAAE,CAAC;QACvC,MAAM,SAAS,GAAG,IAAA,qBAAY,GAAE,CAAC;QACjC,MAAM,KAAK,GAAG,IAAA,iCAAkB,GAAE,CAAC;QAEnC,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACvD,IAAI,MAAM,IAAI,YAAY;gBAAE,KAAK,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,CACL,CAAC,aAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAC/B;QAAA,CAAC,eAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,mCAAgB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAG,CAAC,CAC3D;UAAA,CAAC,eAAe,CACd,IAAI,KAAK,CAAC;QACV,oEAAoE;QACpE,gEAAgE;QAChE,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAEzB;QAAA,EAAE,eAAK,CAAC,QAAQ,CAClB;MAAA,EAAE,aAAK,CAAC,CACT,CAAC;IACJ,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,SAAS,CAAC,WAAW,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC;IAClD,CAAC;IAED,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IACrC,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAgB,oBAAoB,CAClC,KAAgB,EAChB,OAAgC;IAEhC,OAAO,CAAC,IAAI,EAAE,EAAE;QACd,uCAAuC;QACvC,MAAM,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;QAChF,MAAM,YAAY,GAAG,OAAO,aAAa,KAAK,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;QAC/F,MAAM,aAAa,GAAG,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAChF,MAAM,MAAM,GAAG;YACb,GAAG,YAAY;YACf,GAAG,aAAa;SACjB,CAAC;QAEF,4DAA4D;QAC5D,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,CAAC,eAAe,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;YAC7C,MAAM,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;YACjC,qFAAqF;YACrF,MAAM,CAAC,eAAe,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;QAC1D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,aAAa,CAC3B,KAAgB,EAChB,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAK,KAA2B,EAAE;IAEvD,OAAO,CACL,CAAC,mBAAM,CACL,IAAI,KAAK,CAAC,CACV,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAClB,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CACjB,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,OAAO,CAAC,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAC9C,YAAY,CAAC,CAAC,GAAG,EAAE,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,EACtD,CACH,CAAC;AACJ,CAAC;AAED,SAAgB,aAAa,CAC3B,IAAY,EACZ,UAAwD,EAAE;IAE1D,OAAO,IAAI;SACR,KAAK,CAAC,GAAG,CAAC;SACV,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;QACf,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC;QACtE,CAAC;aAAM,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC", "sourcesContent": ["'use client';\n\nimport {\n  useIsFocused,\n  useStateForPath,\n  type EventMapBase,\n  type NavigationState,\n  type ParamListBase,\n  type RouteProp,\n  type ScreenListeners,\n} from '@react-navigation/native';\nimport React from 'react';\n\nimport { LoadedRoute, Route, RouteNode, sortRoutesWithInitial, useRouteNode } from './Route';\nimport { useExpoRouterStore } from './global-state/storeContext';\nimport EXPO_ROUTER_IMPORT_MODE from './import-mode';\nimport { Screen } from './primitives';\nimport { UnknownOutputParams } from './types';\nimport { EmptyRoute } from './views/EmptyRoute';\nimport { SuspenseFallback } from './views/SuspenseFallback';\nimport { Try } from './views/Try';\n\nexport type ScreenProps<\n  TOptions extends Record<string, any> = Record<string, any>,\n  TState extends NavigationState = NavigationState,\n  TEventMap extends EventMapBase = EventMapBase,\n> = {\n  /** Name is required when used inside a Layout component. */\n  name?: string;\n  /**\n   * Redirect to the nearest sibling route.\n   * If all children are `redirect={true}`, the layout will render `null` as there are no children to render.\n   */\n  redirect?: boolean;\n  initialParams?: Record<string, any>;\n  options?:\n    | TOptions\n    | ((prop: { route: RouteProp<ParamListBase, string>; navigation: any }) => TOptions);\n\n  listeners?:\n    | ScreenListeners<TState, TEventMap>\n    | ((prop: {\n        route: RouteProp<ParamListBase, string>;\n        navigation: any;\n      }) => ScreenListeners<TState, TEventMap>);\n\n  getId?: ({ params }: { params?: Record<string, any> }) => string | undefined;\n\n  dangerouslySingular?: SingularOptions;\n};\n\nexport type SingularOptions =\n  | boolean\n  | ((name: string, params: UnknownOutputParams) => string | undefined);\n\nfunction getSortedChildren(\n  children: RouteNode[],\n  order: ScreenProps[] = [],\n  initialRouteName?: string\n): { route: RouteNode; props: Partial<ScreenProps> }[] {\n  if (!order?.length) {\n    return children\n      .sort(sortRoutesWithInitial(initialRouteName))\n      .map((route) => ({ route, props: {} }));\n  }\n  const entries = [...children];\n\n  const ordered = order\n    .map(\n      ({\n        name,\n        redirect,\n        initialParams,\n        listeners,\n        options,\n        getId,\n        dangerouslySingular: singular,\n      }) => {\n        if (!entries.length) {\n          console.warn(\n            `[Layout children]: Too many screens defined. Route \"${name}\" is extraneous.`\n          );\n          return null;\n        }\n        const matchIndex = entries.findIndex((child) => child.route === name);\n        if (matchIndex === -1) {\n          console.warn(\n            `[Layout children]: No route named \"${name}\" exists in nested children:`,\n            children.map(({ route }) => route)\n          );\n          return null;\n        } else {\n          // Get match and remove from entries\n          const match = entries[matchIndex];\n          entries.splice(matchIndex, 1);\n\n          // Ensure to return null after removing from entries.\n          if (redirect) {\n            if (typeof redirect === 'string') {\n              throw new Error(`Redirecting to a specific route is not supported yet.`);\n            }\n            return null;\n          }\n\n          if (getId) {\n            console.warn(\n              `Deprecated: prop 'getId' on screen ${name} is deprecated. Please rename the prop to 'dangerouslySingular'`\n            );\n            if (singular) {\n              console.warn(\n                `Screen ${name} cannot use both getId and dangerouslySingular together.`\n              );\n            }\n          } else if (singular) {\n            // If singular is set, use it as the getId function.\n            if (typeof singular === 'string') {\n              getId = () => singular;\n            } else if (typeof singular === 'function' && name) {\n              getId = (options) => singular(name, options.params || {});\n            } else if (singular === true && name) {\n              getId = (options) => getSingularId(name, options);\n            }\n          }\n\n          return {\n            route: match,\n            props: { initialParams, listeners, options, getId },\n          };\n        }\n      }\n    )\n    .filter(Boolean) as {\n    route: RouteNode;\n    props: Partial<ScreenProps>;\n  }[];\n\n  // Add any remaining children\n  ordered.push(\n    ...entries.sort(sortRoutesWithInitial(initialRouteName)).map((route) => ({ route, props: {} }))\n  );\n\n  return ordered;\n}\n\n/**\n * @returns React Navigation screens sorted by the `route` property.\n */\nexport function useSortedScreens(\n  order: ScreenProps[],\n  protectedScreens: Set<string>\n): React.ReactNode[] {\n  const node = useRouteNode();\n\n  const sorted = node?.children?.length\n    ? getSortedChildren(node.children, order, node.initialRouteName)\n    : [];\n  return React.useMemo(\n    () =>\n      sorted\n        .filter((item) => !protectedScreens.has(item.route.route))\n        .map((value) => {\n          return routeToScreen(value.route, value.props);\n        }),\n    [sorted, protectedScreens]\n  );\n}\n\nfunction fromImport(value: RouteNode, { ErrorBoundary, ...component }: LoadedRoute) {\n  // If possible, add a more helpful display name for the component stack to improve debugging of React errors such as `Text strings must be rendered within a <Text> component.`.\n  if (component?.default && __DEV__) {\n    component.default.displayName ??= `${component.default.name ?? 'Route'}(${value.contextKey})`;\n  }\n\n  if (ErrorBoundary) {\n    const Wrapped = React.forwardRef((props: any, ref: any) => {\n      const children = React.createElement(component.default || EmptyRoute, {\n        ...props,\n        ref,\n      });\n      return <Try catch={ErrorBoundary}>{children}</Try>;\n    });\n\n    if (__DEV__) {\n      Wrapped.displayName = `ErrorBoundary(${value.contextKey})`;\n    }\n\n    return {\n      default: Wrapped,\n    };\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (\n      typeof component.default === 'object' &&\n      component.default &&\n      Object.keys(component.default).length === 0\n    ) {\n      return { default: EmptyRoute };\n    }\n  }\n\n  return { default: component.default };\n}\n\nfunction fromLoadedRoute(value: RouteNode, res: LoadedRoute) {\n  if (!(res instanceof Promise)) {\n    return fromImport(value, res);\n  }\n\n  return res.then(fromImport.bind(null, value));\n}\n\n// TODO: Maybe there's a more React-y way to do this?\n// Without this store, the process enters a recursive loop.\nconst qualifiedStore = new WeakMap<RouteNode, React.ComponentType<any>>();\n\n/** Wrap the component with various enhancements and add access to child routes. */\nexport function getQualifiedRouteComponent(value: RouteNode) {\n  if (qualifiedStore.has(value)) {\n    return qualifiedStore.get(value)!;\n  }\n\n  let ScreenComponent:\n    | React.ForwardRefExoticComponent<React.RefAttributes<unknown>>\n    | React.ComponentType<any>;\n\n  // TODO: This ensures sync doesn't use React.lazy, but it's not ideal.\n  if (EXPO_ROUTER_IMPORT_MODE === 'lazy') {\n    ScreenComponent = React.lazy(async () => {\n      const res = value.loadRoute();\n      return fromLoadedRoute(value, res) as Promise<{\n        default: React.ComponentType<any>;\n      }>;\n    });\n\n    if (__DEV__) {\n      ScreenComponent.displayName = `AsyncRoute(${value.route})`;\n    }\n  } else {\n    const res = value.loadRoute();\n    ScreenComponent = fromImport(value, res).default!;\n  }\n  function BaseRoute({\n    // Remove these React Navigation props to\n    // enforce usage of expo-router hooks (where the query params are correct).\n    route,\n    navigation,\n\n    // Pass all other props to the component\n    ...props\n  }: any) {\n    const stateForPath = useStateForPath();\n    const isFocused = useIsFocused();\n    const store = useExpoRouterStore();\n\n    if (isFocused) {\n      const state = navigation.getState();\n      const isLeaf = !('state' in state.routes[state.index]);\n      if (isLeaf && stateForPath) store.setFocusedState(stateForPath);\n    }\n\n    return (\n      <Route node={value} route={route}>\n        <React.Suspense fallback={<SuspenseFallback route={value} />}>\n          <ScreenComponent\n            {...props}\n            // Expose the template segment path, e.g. `(home)`, `[foo]`, `index`\n            // the intention is to make it possible to deduce shared routes.\n            segment={value.route}\n          />\n        </React.Suspense>\n      </Route>\n    );\n  }\n\n  if (__DEV__) {\n    BaseRoute.displayName = `Route(${value.route})`;\n  }\n\n  qualifiedStore.set(value, BaseRoute);\n  return BaseRoute;\n}\n\nexport function screenOptionsFactory(\n  route: RouteNode,\n  options?: ScreenProps['options']\n): ScreenProps['options'] {\n  return (args) => {\n    // Only eager load generated components\n    const staticOptions = route.generated ? route.loadRoute()?.getNavOptions : null;\n    const staticResult = typeof staticOptions === 'function' ? staticOptions(args) : staticOptions;\n    const dynamicResult = typeof options === 'function' ? options?.(args) : options;\n    const output = {\n      ...staticResult,\n      ...dynamicResult,\n    };\n\n    // Prevent generated screens from showing up in the tab bar.\n    if (route.generated) {\n      output.tabBarItemStyle = { display: 'none' };\n      output.tabBarButton = () => null;\n      // TODO: React Navigation doesn't provide a way to prevent rendering the drawer item.\n      output.drawerItemStyle = { height: 0, display: 'none' };\n    }\n\n    return output;\n  };\n}\n\nexport function routeToScreen(\n  route: RouteNode,\n  { options, getId, ...props }: Partial<ScreenProps> = {}\n) {\n  return (\n    <Screen\n      {...props}\n      name={route.route}\n      key={route.route}\n      getId={getId}\n      options={screenOptionsFactory(route, options)}\n      getComponent={() => getQualifiedRouteComponent(route)}\n    />\n  );\n}\n\nexport function getSingularId(\n  name: string,\n  options: { params?: Record<string, any> | undefined } = {}\n) {\n  return name\n    .split('/')\n    .map((segment) => {\n      if (segment.startsWith('[...')) {\n        return options.params?.[segment.slice(4, -1)]?.join('/') || segment;\n      } else if (segment.startsWith('[')) {\n        return options.params?.[segment.slice(1, -1)] || segment;\n      } else {\n        return segment;\n      }\n    })\n    .join('/');\n}\n"]}