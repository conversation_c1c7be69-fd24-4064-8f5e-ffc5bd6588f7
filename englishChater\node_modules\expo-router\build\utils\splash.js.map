{"version": 3, "file": "splash.js", "sourceRoot": "", "sources": ["../../src/utils/splash.ts"], "names": [], "mappings": ";;AAMA,oBAMC;AAED,8BAEC;AAED,oDAMC;AAED,wEAoBC;AAED,4DAMC;AAtDD,+BAAmD;AAEnD,MAAM,YAAY,GAAG,IAAA,kCAA2B,EAAC,kBAAkB,CAAC,CAAC;AAErE,IAAI,wBAAwB,GAAG,KAAK,CAAC;AAErC,SAAgB,IAAI;IAClB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,CAAC;AACtB,CAAC;AAEM,KAAK,UAAU,SAAS;IAC7B,IAAI,EAAE,CAAC;AACT,CAAC;AAEM,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAED,OAAO,YAAY,CAAC,oBAAoB,EAAE,CAAC;AAC7C,CAAC;AAEM,KAAK,UAAU,8BAA8B;IAClD,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC9B,oGAAoG;QACpG,oEAAoE;QACpE,IAAI,UAAU,EAAE,gBAAgB,EAAE,CAAC;YACjC,MAAM,eAAe,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACtD,UAAU,CAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBAC7C,IAAI,EAAE,CAAC;gBACP,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,wBAAwB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED,OAAO,YAAY,CAAC,4BAA4B,EAAE,CAAC;AACrD,CAAC;AAEM,KAAK,UAAU,wBAAwB;IAC5C,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,YAAY,CAAC,sBAAsB,EAAE,CAAC;AAC/C,CAAC", "sourcesContent": ["import { requireOptionalNativeModule } from 'expo';\n\nconst SplashModule = requireOptionalNativeModule('ExpoSplashScreen');\n\nlet _initializedErrorHandler = false;\n\nexport function hide() {\n  if (!SplashModule) {\n    return;\n  }\n\n  SplashModule.hide();\n}\n\nexport async function hideAsync(): Promise<void> {\n  hide();\n}\n\nexport async function preventAutoHideAsync() {\n  if (!SplashModule) {\n    return;\n  }\n\n  return SplashModule.preventAutoHideAsync();\n}\n\nexport async function _internal_preventAutoHideAsync(): Promise<boolean> {\n  if (!SplashModule) {\n    return false;\n  }\n\n  if (!_initializedErrorHandler) {\n    // Append error handling to ensure any uncaught exceptions result in the splash screen being hidden.\n    // This prevents the splash screen from floating over error screens.\n    if (ErrorUtils?.getGlobalHandler) {\n      const originalHandler = ErrorUtils.getGlobalHandler();\n      ErrorUtils.setGlobalHandler((error, isFatal) => {\n        hide();\n        originalHandler(error, isFatal);\n      });\n    }\n\n    _initializedErrorHandler = true;\n  }\n\n  return SplashModule.internalPreventAutoHideAsync();\n}\n\nexport async function _internal_maybeHideAsync() {\n  if (!SplashModule) {\n    return false;\n  }\n\n  return SplashModule.internalMaybeHideAsync();\n}\n"]}