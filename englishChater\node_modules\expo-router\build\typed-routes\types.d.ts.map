{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../src/typed-routes/types.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AACH,yBAAiB,UAAU,CAAC;IAE1B,UAAiB,QAAQ;KAAG;CAC7B;AAED,MAAM,MAAM,UAAU,GAAG;IACvB,6BAA6B;IAC7B,QAAQ,EAAE,MAAM,CAAC;IACjB,yCAAyC;IACzC,MAAM,CAAC,EAAE,kBAAkB,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG;IAClC,6BAA6B;IAC7B,QAAQ,EAAE,MAAM,CAAC;IACjB,+CAA+C;IAC/C,MAAM,CAAC,EAAE,kBAAkB,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG;IACnC,4BAA4B;IAC5B,QAAQ,EAAE,MAAM,CAAC;IACjB,+CAA+C;IAC/C,MAAM,CAAC,EAAE,mBAAmB,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG,KAAK,MAAM,EAAE,GAAG,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC;AACvE,MAAM,MAAM,YAAY,GAAG,IAAI,MAAM,EAAE,GAAG,IAAI,MAAM,EAAE,CAAC;AACvD,MAAM,MAAM,kBAAkB,GAAG,GAAG,MAAM,IAAI,MAAM,EAAE,GAAG,KAAK,MAAM,EAAE,CAAC;AACvE,MAAM,MAAM,KAAK,GAAG,OAAO,CACzB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,UAAU,CAAC,EAAE,sDAAsD;AACzF,AADmC,sDAAsD;AACzF,kBAAkB,GAAG,kBAAkB,CACxC,CAAC;AAEF;;;;;;;;;GASG;AACH,MAAM,MAAM,IAAI,CAAC,CAAC,SAAS,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,CAAC,SAAS;IAAE,IAAI,EAAE,GAAG,CAAA;CAAE,GAC3F,CAAC,CAAC,MAAM,CAAC,GACT,MAAM,GAAG,UAAU,CAAC;AAKxB,MAAM,MAAM,eAAe,CAAC,CAAC,SAAS,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,CAAC,SAAS;IAC3F,eAAe,EAAE,GAAG,CAAC;CACtB,GACG,CAAC,CAAC,iBAAiB,CAAC,GACpB,qBAAqB,CAAC;AAK1B,MAAM,MAAM,gBAAgB,CAAC,CAAC,SAAS,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,IAAI,CAAC,SAAS;IAC5F,gBAAgB,EAAE,GAAG,CAAC;CACvB,GACG,CAAC,CAAC,kBAAkB,CAAC,GACrB,sBAAsB,CAAC;AAE3B;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,SAAS,KAAK,IAC1C,OAAO,CAAC,IAAI,EAAE;IAAE,QAAQ,EAAE,CAAC,CAAA;CAAE,CAAC,SAAS,KAAK,GACxC,eAAe,SAAS,MAAM,CAAC,GAC7B,CAAC,SAAS,MAAM,CAAC,UAAU,GAAG,QAAQ,EAAE,GAAG,CAAC,GAC1C,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GACrB,CAAC,CAAC,QAAQ,CAAC,GACX,KAAK,GACP,KAAK,GACP,KAAK,GACP,OAAO,CAAC,eAAe,EAAE;IAAE,QAAQ,EAAE,CAAC,CAAA;CAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;AAE1D;;GAEG;AACH,MAAM,MAAM,iBAAiB,CAAC,CAAC,SAAS,KAAK,IAC3C,OAAO,CAAC,gBAAgB,EAAE;IAAE,QAAQ,EAAE,CAAC,CAAA;CAAE,CAAC,SAAS,KAAK,GACpD,gBAAgB,SAAS,MAAM,CAAC,GAC9B,CAAC,SAAS,MAAM,CAAC,UAAU,GAAG,QAAQ,EAAE,GAAG,CAAC,GAC1C,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,GACrB,CAAC,CAAC,QAAQ,CAAC,GACX,KAAK,GACP,KAAK,GACP,KAAK,GACP,OAAO,CAAC,gBAAgB,EAAE;IAAE,QAAQ,EAAE,CAAC,CAAA;CAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,WAAW,CAAC,CAAC,SAAS,KAAK,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAEhE;;;;;GAKG;AACH,MAAM,MAAM,kBAAkB,GAAG,MAAM,CACrC,MAAM,EACN,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CACzD,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC,CAAC;AAEpE;;;;;;;;;;;;;GAaG;AACH,MAAM,MAAM,eAAe,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,MAAM,GACrE,KAAK,GACL,CAAC,SAAS,GAAG,MAAM,IAAI,MAAM,EAAE,GAC7B,KAAK,GACL,CAAC,SAAS,GAAG,MAAM,GAAG,YAAY,EAAE,GAClC,KAAK,GACL,CAAC,SAAS,EAAE,GACV,KAAK,GACL,CAAC,SAAS,IAAI,MAAM,GAAG,GACrB,KAAK,GACL,CAAC,SAAS,IAAI,MAAM,GAAG,GACrB,KAAK,GACL,CAAC,CAAC;AAEhB;;;;GAIG;AACH,MAAM,MAAM,YAAY,CAAC,CAAC,SAAS,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAEpE;;GAEG;AACH,MAAM,MAAM,aAAa,CAAC,cAAc,SAAS,KAAK,GAAG,MAAM,EAAE,IAAI,cAAc,SAAS,MAAM,EAAE,GAChG,cAAc,GACd,cAAc,SAAS,IAAI,MAAM,EAAE,GACjC,KAAK,GACL,cAAc,SAAS,EAAE,GACvB,KAAK,GACL,cAAc,SAAS,IAAI,MAAM,KAAK,EAAE,GACtC,aAAa,CAAC,KAAK,CAAC,GACpB,cAAc,SAAS,GAAG,MAAM,KAAK,IAAI,MAAM,EAAE,GAC/C,aAAa,CAAC,KAAK,CAAC,GACpB,cAAc,SAAS,GAAG,MAAM,KAAK,IAAI,MAAM,EAAE,GAC/C,aAAa,CAAC,KAAK,CAAC,GACpB,cAAc,SAAS,GAAG,MAAM,KAAK,IAAI,MAAM,KAAK,EAAE,GACpD,CAAC,KAAK,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,GAChC,CAAC,cAAc,CAAC,CAAC"}