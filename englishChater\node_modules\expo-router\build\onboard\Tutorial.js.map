{"version": 3, "file": "Tutorial.js", "sourceRoot": "", "sources": ["../../src/onboard/Tutorial.tsx"], "names": [], "mappings": ";;;;;AAUA,4BAgEC;AA1ED,kDAA0B;AAC1B,+CAAkF;AAClF,mFAA8D;AAE9D,uDAAyD;AACzD,wCAAkC;AAClC,kDAA+C;AAE/C,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI,CAAC;AAElE,SAAgB,QAAQ;IACtB,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;YAC1B,gEAAgE;YAChE,uCAAuC;YACvC,+DAA+D;YAC/D,uGAAuG;YACvG,oFAAoF;YACpF,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;gBACjE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC;YACD,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;gBAC5E,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,iBAAiB,CAAC;YAC5C,CAAC;QACH,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CACL,CAAC,6CAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CACrC;MAAA,CAAC,wBAAS,CAAC,QAAQ,CAAC,eAAe,EACnC;MAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAC5B;QAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAClC;UAAA,CAAC,oBAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC,EACpF;QAAA,EAAE,mBAAI,CACN;QAAA,CAAC,mBAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACtD;;QACF,EAAE,mBAAI,CACN;QAAA,CAAC,mBAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CACjF;kCAAwB,CAAC,uBAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CACtE;UAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,mBAAI,CAAE;QAC5D,EAAE,mBAAI,CACN;QAAA,CAAC,mBAAI,CACH;UAAA,CAAC,cAAI,CACH,IAAI,CAAC,4CAA4C,CACjD,IAAI,uBAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAC9E;YAAA,CAAC,qBAAS,CACR;cAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;gBACL,MAAM,CAAC,IAAI;gBACX,uBAAQ,CAAC,MAAM,CAAC;oBACd,GAAG,EAAE;wBACH,kBAAkB,EAAE,OAAO;wBAC3B,YAAY,EAAE,EAAE;qBACjB;iBACF,CAAC;gBACF,OAAO,IAAI;oBACT,OAAO,EAAE,GAAG;oBACZ,kBAAkB,EAAE,WAAW;iBAChC;gBACD,OAAO,IAAI;oBACT,OAAO,EAAE,GAAG;iBACb;aACF,CAAC,CACF;;gBACF,EAAE,mBAAI,CAAC,CACR,CACH;YAAA,EAAE,qBAAS,CACb;UAAA,EAAE,cAAI,CACR;QAAA,EAAE,mBAAI,CACN;QAAA,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,AAAD,EAAG,CACjC;MAAA,EAAE,mBAAI,CACR;IAAA,EAAE,6CAAY,CAAC,CAChB,CAAC;AACJ,CAAC;AAED,SAAS,UAAU;IACjB,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAyB,CAAC;IAClD,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QAC7B,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC/B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AACrC,CAAC;AAED,SAAS,MAAM;IACb,OAAO,CACL,CAAC,qBAAS,CACR,OAAO,CAAC,CAAC,GAAG,EAAE;YACZ,IAAA,sCAAoB,GAAE,CAAC;QACzB,CAAC,CAAC,CACF,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CACrB;MAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;gBACL,MAAM,CAAC,eAAe;gBACtB,OAAO,IAAI;oBACT,eAAe,EAAE,MAAM;iBACxB;gBACD,OAAO;oBACL,uBAAQ,CAAC,MAAM,CAAC;wBACd,GAAG,EAAE;4BACH,SAAS,EAAE,aAAa;4BACxB,kBAAkB,EAAE,OAAO;yBAC5B;wBACD,OAAO,EAAE;4BACP,eAAe,EAAE,MAAM;yBACxB;qBACF,CAAC;aACL,CAAC,CACF;UAAA,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;gBACL,MAAM,CAAC,IAAI;gBACX,OAAO,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;gBAC5B,OAAO;oBACL,uBAAQ,CAAC,MAAM,CAAC;wBACd,MAAM,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;qBAC1B,CAAC;aACL,CAAC,CACF;YAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,mBAAI,CAAE,OAAM,CAAC,UAAU,EAAE,CAC/D;;UACF,EAAE,mBAAI,CACR;QAAA,EAAE,mBAAI,CAAC,CACR,CACH;IAAA,EAAE,qBAAS,CAAC,CACb,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,UAAU,EAAE;QACV,eAAe,EAAE,MAAM;QACvB,IAAI,EAAE,CAAC;KACR;IACD,SAAS,EAAE;QACT,IAAI,EAAE,CAAC;QACP,OAAO,EAAE,EAAE;QACX,aAAa,EAAE,EAAE;QACjB,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;QACxB,gBAAgB,EAAE,MAAM;QACxB,GAAG,EAAE,EAAE;QACP,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,QAAQ,EAAE,GAAG;aACd;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,MAAM;aACd;SACF,CAAC;KACH;IACD,eAAe,EAAE;QACf,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;QACxB,eAAe,EAAE,SAAS;QAC1B,YAAY,EAAE,EAAE;QAChB,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,SAAS;QACtB,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,EAAE;QACV,YAAY,EAAE,CAAC;KAChB;IACD,QAAQ,EAAE;QACR,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,EAAE;KACX;IACD,KAAK,EAAE;QACL,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,EAAE;aACf;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,EAAE;aACf;SACF,CAAC;QACF,KAAK,EAAE,MAAM;QACb,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,QAAQ;KACpB;IACD,eAAe,EAAE;QACf,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,kBAAkB,EAAE,OAAO;aAC5B;SACF,CAAC;QACF,eAAe,EAAE,aAAa;QAC9B,WAAW,EAAE,MAAM;QACnB,WAAW,EAAE,CAAC;QACd,eAAe,EAAE,EAAE;QACnB,iBAAiB,EAAE,EAAE;QACrB,YAAY,EAAE,CAAC;KAChB;IACD,MAAM,EAAE;QACN,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,SAAS,EAAE,EAAE;aACd;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,UAAU;gBACpB,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,QAAQ;aACnB;SACF,CAAC;KACH;IACD,IAAI,EAAE;QACJ,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,kBAAkB,EAAE,OAAO;gBAC3B,UAAU,EAAE,oBAAoB;aACjC;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,uBAAQ,CAAC,MAAM,CAAC;oBAC1B,GAAG,EAAE,aAAa;oBAClB,OAAO,EAAE,WAAW;iBACrB,CAAC;aACH;SACF,CAAC;QACF,KAAK,EAAE,MAAM;QACb,SAAS,EAAE,QAAQ;QACnB,UAAU,EAAE,MAAM;QAClB,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,MAAM;KACnB;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,QAAQ;KACpB;IACD,IAAI,EAAE;QACJ,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,EAAE;QACd,SAAS,EAAE,QAAQ;QACnB,KAAK,EAAE,SAAS;QAChB,SAAS,EAAE,EAAE;QACb,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,YAAY,EAAE,EAAE;aACjB;SACF,CAAC;KACH;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS;KACjB;CACF,CAAC,CAAC", "sourcesContent": ["import React from 'react';\nimport { Platform, StatusBar, StyleSheet, Text, View, Image } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\n\nimport { createEntryFileAsync } from './createEntryFile';\nimport { Link } from '../exports';\nimport { Pressable } from '../views/Pressable';\n\nconst canAutoTouchFile = process.env.EXPO_ROUTER_APP_ROOT != null;\n\nexport function Tutorial() {\n  React.useEffect(() => {\n    if (Platform.OS === 'web') {\n      // Reset the route on web so the initial route isn't a 404 after\n      // the user has created the entry file.\n      // This is useful for cases where you are testing the tutorial.\n      // To test: touch the new file, then navigate to a missing route `/foobar`, then delete the app folder.\n      // you should see the tutorial again and be able to create the entry file once more.\n      if (typeof location !== 'undefined' && location.pathname !== '/') {\n        location.replace('/');\n      }\n      if (typeof window !== 'undefined' && typeof window.document !== 'undefined') {\n        window.document.title = 'Welcome to Expo';\n      }\n    }\n  }, []);\n\n  return (\n    <SafeAreaView style={styles.background}>\n      <StatusBar barStyle=\"light-content\" />\n      <View style={styles.container}>\n        <View style={styles.logotypeWrapper}>\n          <Image style={styles.logotype} source={require('expo-router/assets/logotype.png')} />\n        </View>\n        <Text role=\"heading\" aria-level={1} style={styles.title}>\n          Welcome to Expo\n        </Text>\n        <Text role=\"heading\" aria-level={2} style={[styles.subtitle, styles.textSecondary]}>\n          Start by creating a file{Platform.OS !== 'web' ? '\\n' : ' '}in the{' '}\n          <Text style={{ fontWeight: '600' }}>{getRootDir()}</Text> directory.\n        </Text>\n        <Text>\n          <Link\n            href=\"https://docs.expo.dev/router/introduction/\"\n            {...Platform.select({ web: { target: '_blank' }, native: { asChild: true } })}>\n            <Pressable>\n              {({ hovered, pressed }) => (\n                <Text\n                  style={[\n                    styles.link,\n                    Platform.select({\n                      web: {\n                        transitionDuration: '200ms',\n                        marginBottom: 12,\n                      },\n                    }),\n                    hovered && {\n                      opacity: 0.8,\n                      textDecorationLine: 'underline',\n                    },\n                    pressed && {\n                      opacity: 0.8,\n                    },\n                  ]}>\n                  Learn more about Expo Router in the documentation.\n                </Text>\n              )}\n            </Pressable>\n          </Link>\n        </Text>\n        {canAutoTouchFile && <Button />}\n      </View>\n    </SafeAreaView>\n  );\n}\n\nfunction getRootDir() {\n  const dir = process.env.EXPO_ROUTER_ABS_APP_ROOT!;\n  if (dir.match(/\\/src\\/app$/)) {\n    return 'src/app';\n  } else if (dir.match(/\\/app$/)) {\n    return 'app';\n  }\n  return dir.split('/').pop() ?? dir;\n}\n\nfunction Button() {\n  return (\n    <Pressable\n      onPress={() => {\n        createEntryFileAsync();\n      }}\n      style={styles.button}>\n      {({ pressed, hovered }) => (\n        <View\n          style={[\n            styles.buttonContainer,\n            hovered && {\n              backgroundColor: '#fff',\n            },\n            pressed &&\n              Platform.select({\n                web: {\n                  transform: 'scale(0.98)',\n                  transitionDuration: '200ms',\n                },\n                default: {\n                  backgroundColor: '#fff',\n                },\n              }),\n          ]}>\n          <Text\n            style={[\n              styles.code,\n              hovered && { color: '#000' },\n              pressed &&\n                Platform.select({\n                  native: { color: '#000' },\n                }),\n            ]}>\n            <Text style={styles.textSecondary}>$</Text> touch {getRootDir()}\n            /index.tsx\n          </Text>\n        </View>\n      )}\n    </Pressable>\n  );\n}\n\nconst styles = StyleSheet.create({\n  background: {\n    backgroundColor: '#000',\n    flex: 1,\n  },\n  container: {\n    flex: 1,\n    padding: 24,\n    paddingBottom: 64,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginHorizontal: 'auto',\n    gap: 16,\n    ...Platform.select({\n      web: {\n        maxWidth: 960,\n      },\n      native: {\n        width: '100%',\n      },\n    }),\n  },\n  logotypeWrapper: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    backgroundColor: '#151718',\n    borderRadius: 12,\n    borderWidth: 1,\n    borderStyle: 'solid',\n    borderColor: '#313538',\n    width: 78,\n    height: 78,\n    marginBottom: 8,\n  },\n  logotype: {\n    width: 48,\n    height: 44,\n  },\n  title: {\n    ...Platform.select({\n      web: {\n        fontSize: 64,\n        lineHeight: 64,\n      },\n      default: {\n        fontSize: 56,\n        lineHeight: 56,\n      },\n    }),\n    color: '#fff',\n    fontWeight: '800',\n    textAlign: 'center',\n  },\n  buttonContainer: {\n    ...Platform.select({\n      web: {\n        transitionDuration: '200ms',\n      },\n    }),\n    backgroundColor: 'transparent',\n    borderColor: '#fff',\n    borderWidth: 2,\n    paddingVertical: 12,\n    paddingHorizontal: 24,\n    borderRadius: 8,\n  },\n  button: {\n    ...Platform.select({\n      web: {\n        marginTop: 12,\n      },\n      native: {\n        position: 'absolute',\n        bottom: 24,\n        left: 32,\n        right: 32,\n        overflow: 'hidden',\n      },\n    }),\n  },\n  code: {\n    ...Platform.select({\n      web: {\n        transitionDuration: '200ms',\n        fontFamily: 'Courier, monospace',\n      },\n      default: {\n        fontFamily: Platform.select({\n          ios: 'Courier New',\n          android: 'monospace',\n        }),\n      },\n    }),\n    color: '#fff',\n    textAlign: 'center',\n    userSelect: 'none',\n    fontSize: 18,\n    fontWeight: 'bold',\n  },\n  subtitle: {\n    fontSize: 34,\n    fontWeight: '200',\n    textAlign: 'center',\n  },\n  link: {\n    fontSize: 20,\n    lineHeight: 26,\n    textAlign: 'center',\n    color: '#52a9ff',\n    marginTop: 12,\n    ...Platform.select({\n      web: {\n        marginBottom: 24,\n      },\n    }),\n  },\n  textSecondary: {\n    color: '#9ba1a6',\n  },\n});\n"]}