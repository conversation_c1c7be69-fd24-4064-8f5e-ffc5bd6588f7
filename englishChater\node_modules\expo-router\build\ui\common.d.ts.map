{"version": 3, "file": "common.d.ts", "sourceRoot": "", "sources": ["../../src/ui/common.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAC9F,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAE7D,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAGrC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAKhC,eAAO,MAAM,QAAQ,EAAW,KAAK,CAAC,yBAAyB,CAC7D,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CACtC,CAAC;AAEF,eAAO,MAAM,gBAAgB,EAAW,KAAK,CAAC,yBAAyB,CACrE,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAC9C,CAAC;AAEF,MAAM,MAAM,aAAa,GACrB;IACE,IAAI,EAAE,UAAU,CAAC;IACjB,IAAI,EAAE,IAAI,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;CACd,GACD;IACE,IAAI,EAAE,UAAU,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEN,KAAK,sBAAsB,GAAG,OAAO,CAAC,iBAAiB,EAAE;IAAE,IAAI,EAAE,SAAS,CAAA;CAAE,CAAC,CAAC;AAC9E,KAAK,aAAa,GACd;IACE,IAAI,EAAE,UAAU,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,SAAS,CAAC;IACrB,MAAM,EAAE,sBAAsB,CAAC;CAChC,GACD;IAAE,IAAI,EAAE,UAAU,CAAC;IAAC,IAAI,EAAE,MAAM,CAAC;IAAC,IAAI,EAAE,MAAM,CAAA;CAAE,CAAC;AAErD,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,GAAG;IAAE,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC,CAAC;AAE3E,wBAAgB,iBAAiB,CAC/B,QAAQ,EAAE,aAAa,EAAE,EACzB,eAAe,EAAE,SAAS,EAC1B,OAAO,EAAE,cAAc,CAAC,aAAa,CAAC,EACtC,gBAAgB,EAAE,SAAS,GAAG,MAAM,EACpC,gBAAgB,EAAE,UAAU,EAC5B,SAAS,EAAE,SAAS,EACpB,UAAU,EAAE,MAAM;;;EAyInB;AAED,wBAAgB,aAAa,CAC3B,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,EAClE,YAAY,CAAC,EAAE,MAAM,GACpB,sBAAsB,CAsCxB"}