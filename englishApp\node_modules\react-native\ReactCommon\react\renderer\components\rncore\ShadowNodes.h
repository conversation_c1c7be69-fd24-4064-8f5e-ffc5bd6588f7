
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/rncore/EventEmitters.h>
#include <react/renderer/components/rncore/Props.h>
#include <react/renderer/components/rncore/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char ActivityIndicatorViewComponentName[];

/*
 * `ShadowNode` for <ActivityIndicatorView> component.
 */
using ActivityIndicatorViewShadowNode = ConcreteViewShadowNode<
    ActivityIndicatorViewComponentName,
    ActivityIndicatorViewProps,
    ActivityIndicatorViewEventEmitter,
    ActivityIndicatorViewState>;

JSI_EXPORT extern const char AndroidDrawerLayoutComponentName[];

/*
 * `ShadowNode` for <AndroidDrawerLayout> component.
 */
using AndroidDrawerLayoutShadowNode = ConcreteViewShadowNode<
    AndroidDrawerLayoutComponentName,
    AndroidDrawerLayoutProps,
    AndroidDrawerLayoutEventEmitter,
    AndroidDrawerLayoutState>;

JSI_EXPORT extern const char AndroidSwipeRefreshLayoutComponentName[];

/*
 * `ShadowNode` for <AndroidSwipeRefreshLayout> component.
 */
using AndroidSwipeRefreshLayoutShadowNode = ConcreteViewShadowNode<
    AndroidSwipeRefreshLayoutComponentName,
    AndroidSwipeRefreshLayoutProps,
    AndroidSwipeRefreshLayoutEventEmitter,
    AndroidSwipeRefreshLayoutState>;

JSI_EXPORT extern const char DebuggingOverlayComponentName[];

/*
 * `ShadowNode` for <DebuggingOverlay> component.
 */
using DebuggingOverlayShadowNode = ConcreteViewShadowNode<
    DebuggingOverlayComponentName,
    DebuggingOverlayProps,
    DebuggingOverlayEventEmitter,
    DebuggingOverlayState>;

JSI_EXPORT extern const char PullToRefreshViewComponentName[];

/*
 * `ShadowNode` for <PullToRefreshView> component.
 */
using PullToRefreshViewShadowNode = ConcreteViewShadowNode<
    PullToRefreshViewComponentName,
    PullToRefreshViewProps,
    PullToRefreshViewEventEmitter,
    PullToRefreshViewState>;

JSI_EXPORT extern const char SwitchComponentName[];

/*
 * `ShadowNode` for <Switch> component.
 */
using SwitchShadowNode = ConcreteViewShadowNode<
    SwitchComponentName,
    SwitchProps,
    SwitchEventEmitter,
    SwitchState>;

JSI_EXPORT extern const char UnimplementedNativeViewComponentName[];

/*
 * `ShadowNode` for <UnimplementedNativeView> component.
 */
using UnimplementedNativeViewShadowNode = ConcreteViewShadowNode<
    UnimplementedNativeViewComponentName,
    UnimplementedNativeViewProps,
    UnimplementedNativeViewEventEmitter,
    UnimplementedNativeViewState>;

} // namespace facebook::react
