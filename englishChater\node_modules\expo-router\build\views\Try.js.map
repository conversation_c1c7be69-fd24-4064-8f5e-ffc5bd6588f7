{"version": 3, "file": "Try.js", "sourceRoot": "", "sources": ["../../src/views/Try.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,+CAAqF;AAErF,uDAAyC;AACzC,iDAAwD;AAUxD,gFAAgF;AAChF,MAAa,GAAI,SAAQ,iBAKxB;IACC,KAAK,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;IAE7B,MAAM,CAAC,wBAAwB,CAAC,KAAY;QAC1C,mDAAmD;QACnD,YAAY,CAAC,SAAS,EAAE,CAAC;QAEzB,IAAI,OAAO,IAAI,KAAK,YAAY,yBAAgB,EAAE,CAAC;YACjD,0BAA0B;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,CAAC;IACnB,CAAC;IAED,KAAK,GAAG,GAAG,EAAE;QACX,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YACnC,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE;gBACvC,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM;QACJ,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QAC7B,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QACtD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAG,CAAC;IAC5D,CAAC;CACF;AApCD,kBAoCC", "sourcesContent": ["'use client';\n\nimport React, { Component, type ComponentType, type PropsWithChildren } from 'react';\n\nimport * as SplashScreen from './Splash';\nimport { MetroServerError } from '../rsc/router/errors';\n\n/** Props passed to a page's `ErrorBoundary` export. */\nexport type ErrorBoundaryProps = {\n  /** A function that will re-render the route component by clearing the `error` state. */\n  retry: () => Promise<void>;\n  /** The error that was thrown. */\n  error: Error;\n};\n\n// No way to access `getDerivedStateFromError` from a function component afaict.\nexport class Try extends Component<\n  PropsWithChildren<{\n    catch: ComponentType<ErrorBoundaryProps>;\n  }>,\n  { error?: Error }\n> {\n  state = { error: undefined };\n\n  static getDerivedStateFromError(error: Error) {\n    // Force hide the splash screen if an error occurs.\n    SplashScreen.hideAsync();\n\n    if (__DEV__ && error instanceof MetroServerError) {\n      // Throw up to the LogBox.\n      return null;\n    }\n\n    return { error };\n  }\n\n  retry = () => {\n    return new Promise<void>((resolve) => {\n      this.setState({ error: undefined }, () => {\n        resolve();\n      });\n    });\n  };\n\n  render() {\n    const { error } = this.state;\n    const { catch: ErrorBoundary, children } = this.props;\n    if (!error) {\n      return children;\n    }\n    return <ErrorBoundary error={error} retry={this.retry} />;\n  }\n}\n"]}