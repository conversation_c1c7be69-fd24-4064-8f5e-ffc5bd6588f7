{"version": 3, "file": "EmptyRoute.js", "sourceRoot": "", "sources": ["../../src/views/EmptyRoute.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;AAOb,gCAUC;AAfD,kDAA0B;AAE1B,mCAA8C;AAC9C,oCAAwC;AAExC,SAAgB,UAAU;IACxB,MAAM,KAAK,GAAG,IAAA,oBAAY,GAAE,CAAC;IAE7B,OAAO,CACL,CAAC,oBAAY,CACX;MAAA,CAAC,aAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,CACzC;;MACF,EAAE,aAAK,CACT;IAAA,EAAE,oBAAY,CAAC,CAChB,CAAC;AACJ,CAAC", "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nimport { Toast, ToastWrapper } from './Toast';\nimport { useRouteNode } from '../Route';\n\nexport function EmptyRoute() {\n  const route = useRouteNode();\n\n  return (\n    <ToastWrapper>\n      <Toast warning filename={route?.contextKey}>\n        Missing default export\n      </Toast>\n    </ToastWrapper>\n  );\n}\n"]}