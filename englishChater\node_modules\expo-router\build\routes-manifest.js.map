{"version": 3, "file": "routes-manifest.js", "sourceRoot": "", "sources": ["../src/routes-manifest.ts"], "names": [], "mappings": ";;AAmCA,oDAkBC;AArDD,0CAA0C;AAC1C,sBAAsB;AACtB,iDAAyD;AACzD,2DAAwD;AAsBxD,SAAS,uBAAuB,CAAC,MAAgB,EAAE;IACjD,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,CAAC,EAAE,OAAO,KAAI,CAAC,EAAE,CAAC,CAAC;IAE3D,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE;QAC3C,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG;KACjB,CAAC,CAAC;IAEH,OAAO,aAA+B,CAAC;AACzC,CAAC;AAED,SAAgB,oBAAoB,CAClC,KAAe,EACf,OAAgB;IAEhB,mCAAmC;IACnC,MAAM,SAAS,GAAG,IAAA,wBAAS,EAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;QAC1D,GAAG,OAAO;QACV,iBAAiB,EAAE,IAAI;QACvB,2BAA2B,EAAE,IAAI;QACjC,mBAAmB,EAAE,IAAI;QACzB,iBAAiB,EAAE,IAAI;QACvB,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,IAAA,qCAAiB,EAAC,SAAS,CAAC,CAAC;AACtC,CAAC", "sourcesContent": ["// This file runs in Node.js environments.\n// no relative imports\nimport { type Options, getRoutes } from './getRoutesSSR';\nimport { getServerManifest } from './getServerManifest';\nimport { type RequireContext } from './types';\n\nexport { Options };\n\nexport type RouteInfo<TRegex = string> = {\n  file: string;\n  page: string;\n  namedRegex: TRegex;\n  routeKeys: Record<string, string>;\n  permanent?: boolean;\n  methods?: string[];\n};\n\nexport type ExpoRoutesManifestV1<TRegex = string> = {\n  apiRoutes: RouteInfo<TRegex>[];\n  htmlRoutes: RouteInfo<TRegex>[];\n  notFoundRoutes: RouteInfo<TRegex>[];\n  redirects: RouteInfo<TRegex>[];\n  rewrites: RouteInfo<TRegex>[];\n};\n\nfunction createMockContextModule(map: string[] = []) {\n  const contextModule = (_key: string) => ({ default() {} });\n\n  Object.defineProperty(contextModule, 'keys', {\n    value: () => map,\n  });\n\n  return contextModule as RequireContext;\n}\n\nexport function createRoutesManifest(\n  paths: string[],\n  options: Options\n): ExpoRoutesManifestV1 | null {\n  // TODO: Drop this part for Node.js\n  const routeTree = getRoutes(createMockContextModule(paths), {\n    ...options,\n    preserveApiRoutes: true,\n    preserveRedirectAndRewrites: true,\n    ignoreRequireErrors: true,\n    ignoreEntryPoints: true,\n    platform: 'web',\n  });\n\n  if (!routeTree) {\n    return null;\n  }\n  return getServerManifest(routeTree);\n}\n"]}