{"version": 3, "file": "ErrorBoundary.js", "sourceRoot": "", "sources": ["../../src/views/ErrorBoundary.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;AA8Bb,sCAuCC;AAnED,+DAA0E;AAC1E,iCAA4B;AAC5B,+CAAuF;AACvF,mFAA8D;AAE9D,2CAAwC;AAExC,uCAAoC;AACpC,iDAAwD;AAExD,SAAS,iBAAiB,CAAC,EAAE,KAAK,EAAoB;IACpD,OAAO,CACL,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;YACL,YAAY,EAAE,EAAE;YAChB,GAAG,EAAE,CAAC;YACN,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;SAC5D,CAAC,CACF;MAAA,CAAC,mBAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACtD;;MACF,EAAE,mBAAI,CACN;MAAA,CAAC,mBAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAC3F;eAAO,CAAC,KAAK,CAAC,OAAO,CACvB;MAAA,EAAE,mBAAI,CACR;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,SAAgB,aAAa,CAAC,EAAE,KAAK,EAAE,KAAK,EAAsB;IAChE,MAAM,QAAQ,GAAG,IAAA,WAAG,EAAC,uCAAyB,CAAC,CAAC;IAChD,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,mBAAI,CAAC,CAAC,CAAC,6CAAY,CAAC;IAE/C,MAAM,aAAa,GAAG,KAAK,YAAY,yBAAgB,CAAC;IACxD,OAAO,CACL,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAC5B;MAAA,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAC3E;QAAA,CAAC,aAAa,CAAC,CAAC,CAAC,CACf,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAG,CACvC,CAAC,CAAC,CAAC,CACF,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAG,CACpC,CACD;QAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAEzB;;QAAA,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,CACzC,CAAC,WAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CACtE;;UACF,EAAE,WAAI,CAAC,CACR,CACD;QAAA,CAAC,qBAAS,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CACpD;UAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,CAAC,CAClF;cAAA,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;gBACL,MAAM,CAAC,UAAU;gBACjB;oBACE,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO;iBAC9C;aACF,CAAC,CACF;;cACF,EAAE,mBAAI,CACR;YAAA,EAAE,mBAAI,CAAC,CACR,CACH;QAAA,EAAE,qBAAS,CACb;MAAA,EAAE,OAAO,CACX;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,MAAM,mBAAmB,GAAuC;IAC9D,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,uBAAuB;IAC5B,GAAG,EAAE,qBAAqB;IAC1B,GAAG,EAAE,iBAAiB;CACvB,CAAC;AAEF,oGAAoG;AACpG,SAAS,oBAAoB,CAAC,EAAE,KAAK,EAA+B;IAClE,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACrC,KAAK,IAAI,IAAI,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,CAAC;IAE1E,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAE5C,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAEvC,OAAO,CACL,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;YACL,OAAO,EAAE,EAAE;YACX,GAAG,EAAE,CAAC;SACP,CAAC,CACF;MAAA,CAAC,mBAAI,CACH,UAAU,CACV,gBAAgB,CAChB,KAAK,CAAC,CAAC;YACL,QAAQ,EAAE,uBAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;YACnD,UAAU,EAAE,MAAM;YAClB,YAAY,EAAE,CAAC;YACf,KAAK,EAAE,OAAO;SACf,CAAC,CACF;QAAA,CAAC,KAAK,CACR;MAAA,EAAE,mBAAI,CAEN;;MAAA,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAC/B,CAAC,yBAAU,CACT,KAAK,CAAC,CAAC;gBACL,WAAW,EAAE,uBAAuB;gBACpC,cAAc,EAAE,yBAAU,CAAC,aAAa;gBACxC,iBAAiB,EAAE,yBAAU,CAAC,aAAa;gBAC3C,SAAS,EAAE,GAAG;aACf,CAAC,CACF,qBAAqB,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC,CAC9C;UAAA,CAAC,mBAAI,CACH,MAAM,CAAC,sBAAsB,CAC7B,UAAU,CACV,gBAAgB,CAChB,KAAK,CAAC,CAAC;gBACL,KAAK,EAAE,OAAO;aACf,CAAC,CACF;YAAA,CAAC,KAAK,CAAC,OAAO,CAChB;UAAA,EAAE,mBAAI,CACR;QAAA,EAAE,yBAAU,CAAC,CACd,CAAC,CAAC,CAAC,CACF,CAAC,wBAAS,CACR,MAAM,CAAC,sBAAsB,CAC7B,aAAa,CACb,SAAS,CACT,QAAQ,CAAC,CAAC,KAAK,CAAC,CAChB,gBAAgB,CAChB,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CACrB,KAAK,CAAC,CAAC;gBACL,WAAW,EAAE,uBAAuB;gBACpC,cAAc,EAAE,yBAAU,CAAC,aAAa;gBACxC,iBAAiB,EAAE,yBAAU,CAAC,aAAa;gBAC3C,eAAe,EAAE,CAAC;gBAClB,SAAS,EAAE,GAAG;gBACd,KAAK,EAAE,OAAO;aACf,CAAC,EACF,CACH,CAED;;MAAA,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAC9C;MAAA,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAG,CAClD;MAAA,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAG,CAE9C;;MAAA,CAAC,KAAK,CAAC,GAAG,IAAI,CACZ,CAAC,mBAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CACtF;UAAA,CAAC,KAAK,CAAC,GAAG,CACZ;QAAA,EAAE,mBAAI,CAAC,CACR,CACH;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,EAAkC;IAC/D,MAAM,KAAK,GAAG;QACZ,QAAQ,EAAE,EAAE;QACZ,KAAK,EAAE,OAAO;KACf,CAAC;IAEF,OAAO,CACL,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,CAAC,CACrE;MAAA,CAAC,mBAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAC7C;QAAA,CAAC,KAAK,CACR;MAAA,EAAE,mBAAI,CACN;MAAA,CAAC,KAAK,IAAI,CACR,CAAC,mBAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAC5D;UAAA,CAAC,KAAK,CACR;QAAA,EAAE,mBAAI,CAAC,CACR,CACH;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE;QACT,IAAI,EAAE,CAAC;QACP,eAAe,EAAE,OAAO;QACxB,OAAO,EAAE,EAAE;QACX,UAAU,EAAE,SAAS;QACrB,cAAc,EAAE,QAAQ;KACzB;IACD,KAAK,EAAE;QACL,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,uBAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QACnD,UAAU,EAAE,MAAM;KACnB;IACD,UAAU,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,MAAM;QAClB,KAAK,EAAE,OAAO;QACd,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,kBAAkB,EAAE,OAAO;aAC5B;SACF,CAAC;KACH;IACD,WAAW,EAAE;QACX,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,kBAAkB,EAAE,OAAO;aAC5B;SACF,CAAC;QACF,eAAe,EAAE,EAAE;QACnB,iBAAiB,EAAE,EAAE;QACrB,WAAW,EAAE,OAAO;QACpB,WAAW,EAAE,CAAC;QACd,UAAU,EAAE,CAAC;QACb,cAAc,EAAE,QAAQ;QACxB,UAAU,EAAE,QAAQ;KACrB;IACD,IAAI,EAAE;QACJ,UAAU,EAAE,uBAAQ,CAAC,MAAM,CAAC;YAC1B,OAAO,EAAE,SAAS;YAClB,GAAG,EAAE,aAAa;YAClB,OAAO,EAAE,WAAW;SACrB,CAAC;QACF,UAAU,EAAE,KAAK;KAClB;IACD,YAAY,EAAE;QACZ,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,EAAE;KACb;IACD,QAAQ,EAAE;QACR,KAAK,EAAE,OAAO;QACd,QAAQ,EAAE,EAAE;QACZ,YAAY,EAAE,EAAE;KACjB;IACD,IAAI,EAAE;QACJ,KAAK,EAAE,uBAAuB;QAC9B,mBAAmB,EAAE,OAAO;QAC5B,kBAAkB,EAAE,WAAW;QAC/B,QAAQ,EAAE,EAAE;QACZ,SAAS,EAAE,QAAQ;KACpB;CACF,CAAC,CAAC", "sourcesContent": ["'use client';\n\nimport { BottomTabBarHeightContext } from '@react-navigation/bottom-tabs';\nimport { use } from 'react';\nimport { StyleSheet, Text, View, Platform, ScrollView, TextInput } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\n\nimport { Pressable } from './Pressable';\nimport { ErrorBoundaryProps } from './Try';\nimport { Link } from '../link/Link';\nimport { ReactServerError } from '../rsc/router/errors';\n\nfunction StandardErrorView({ error }: { error: Error }) {\n  return (\n    <View\n      style={{\n        marginBottom: 12,\n        gap: 4,\n        flexWrap: process.env.EXPO_OS === 'web' ? 'wrap' : 'nowrap',\n      }}>\n      <Text role=\"heading\" aria-level={1} style={styles.title}>\n        Something went wrong\n      </Text>\n      <Text testID=\"router_error_message\" role=\"heading\" aria-level={2} style={styles.errorMessage}>\n        Error: {error.message}\n      </Text>\n    </View>\n  );\n}\n\nexport function ErrorBoundary({ error, retry }: ErrorBoundaryProps) {\n  const inTabBar = use(BottomTabBarHeightContext);\n  const Wrapper = inTabBar ? View : SafeAreaView;\n\n  const isServerError = error instanceof ReactServerError;\n  return (\n    <View style={styles.container}>\n      <Wrapper style={{ flex: 1, gap: 8, maxWidth: 720, marginHorizontal: 'auto' }}>\n        {isServerError ? (\n          <ReactServerErrorView error={error} />\n        ) : (\n          <StandardErrorView error={error} />\n        )}\n        <View style={{ flex: 1 }} />\n\n        {process.env.NODE_ENV === 'development' && (\n          <Link testID=\"router_error_sitemap\" href=\"/_sitemap\" style={styles.link}>\n            Sitemap\n          </Link>\n        )}\n        <Pressable testID=\"router_error_retry\" onPress={retry}>\n          {({ hovered, pressed }) => (\n            <View\n              style={[styles.buttonInner, (hovered || pressed) && { backgroundColor: 'white' }]}>\n              <Text\n                style={[\n                  styles.buttonText,\n                  {\n                    color: hovered || pressed ? 'black' : 'white',\n                  },\n                ]}>\n                Retry\n              </Text>\n            </View>\n          )}\n        </Pressable>\n      </Wrapper>\n    </View>\n  );\n}\n\nconst COMMON_ERROR_STATUS: Record<number, string | undefined> = {\n  404: 'NOT_FOUND',\n  500: 'INTERNAL_SERVER_ERROR',\n  503: 'SERVICE_UNAVAILABLE',\n  504: 'GATEWAY_TIMEOUT',\n};\n\n// TODO: This should probably be replaced by a DOM component that loads server errors in the future.\nfunction ReactServerErrorView({ error }: { error: ReactServerError }) {\n  let title = String(error.statusCode);\n  title += ': ' + (COMMON_ERROR_STATUS[error.statusCode] ?? 'Server Error');\n\n  const errorId = error.headers.get('cf-ray');\n\n  const date = error.headers.get('Date');\n\n  return (\n    <View\n      style={{\n        padding: 12,\n        gap: 8,\n      }}>\n      <Text\n        selectable\n        allowFontScaling\n        style={{\n          fontSize: Platform.select({ web: 24, default: 16 }),\n          fontWeight: 'bold',\n          marginBottom: 4,\n          color: 'white',\n        }}>\n        {title}\n      </Text>\n\n      {process.env.EXPO_OS === 'web' ? (\n        <ScrollView\n          style={{\n            borderColor: 'rgba(255,255,255,0.5)',\n            borderTopWidth: StyleSheet.hairlineWidth,\n            borderBottomWidth: StyleSheet.hairlineWidth,\n            maxHeight: 150,\n          }}\n          contentContainerStyle={{ paddingVertical: 4 }}>\n          <Text\n            testID=\"router_error_message\"\n            selectable\n            allowFontScaling\n            style={{\n              color: 'white',\n            }}>\n            {error.message}\n          </Text>\n        </ScrollView>\n      ) : (\n        <TextInput\n          testID=\"router_error_message\"\n          scrollEnabled\n          multiline\n          editable={false}\n          allowFontScaling\n          value={error.message}\n          style={{\n            borderColor: 'rgba(255,255,255,0.5)',\n            borderTopWidth: StyleSheet.hairlineWidth,\n            borderBottomWidth: StyleSheet.hairlineWidth,\n            paddingVertical: 4,\n            maxHeight: 150,\n            color: 'white',\n          }}\n        />\n      )}\n\n      <InfoRow title=\"Code\" right={error.statusCode} />\n      {errorId && <InfoRow title=\"ID\" right={errorId} />}\n      {date && <InfoRow title=\"Date\" right={date} />}\n\n      {error.url && (\n        <Text selectable allowFontScaling style={{ fontSize: 14, opacity: 0.5, color: 'white' }}>\n          {error.url}\n        </Text>\n      )}\n    </View>\n  );\n}\n\nfunction InfoRow({ title, right }: { title: string; right?: any }) {\n  const style = {\n    fontSize: 16,\n    color: 'white',\n  };\n\n  return (\n    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>\n      <Text selectable allowFontScaling style={style}>\n        {title}\n      </Text>\n      {right && (\n        <Text selectable allowFontScaling style={[style, styles.code]}>\n          {right}\n        </Text>\n      )}\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: 'black',\n    padding: 24,\n    alignItems: 'stretch',\n    justifyContent: 'center',\n  },\n  title: {\n    color: 'white',\n    fontSize: Platform.select({ web: 32, default: 24 }),\n    fontWeight: 'bold',\n  },\n  buttonText: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    color: 'black',\n    ...Platform.select({\n      web: {\n        transitionDuration: '100ms',\n      },\n    }),\n  },\n  buttonInner: {\n    ...Platform.select({\n      web: {\n        transitionDuration: '100ms',\n      },\n    }),\n    paddingVertical: 12,\n    paddingHorizontal: 24,\n    borderColor: 'white',\n    borderWidth: 2,\n    marginLeft: 8,\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  code: {\n    fontFamily: Platform.select({\n      default: 'Courier',\n      ios: 'Courier New',\n      android: 'monospace',\n    }),\n    fontWeight: '500',\n  },\n  errorMessage: {\n    color: 'white',\n    fontSize: 16,\n  },\n  subtitle: {\n    color: 'white',\n    fontSize: 14,\n    marginBottom: 12,\n  },\n  link: {\n    color: 'rgba(255,255,255,0.4)',\n    textDecorationStyle: 'solid',\n    textDecorationLine: 'underline',\n    fontSize: 14,\n    textAlign: 'center',\n  },\n});\n"]}