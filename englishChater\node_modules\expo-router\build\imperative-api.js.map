{"version": 3, "file": "imperative-api.js", "sourceRoot": "", "sources": ["../src/imperative-api.tsx"], "names": [], "mappings": ";;;AAmHA,oDAUC;AA7HD,iCAAwD;AAExD,oDAegC;AAgFhC;;GAEG;AACU,QAAA,MAAM,GAAW;IAC5B,QAAQ,EAAR,kBAAQ;IACR,IAAI,EAAJ,cAAI;IACJ,OAAO,EAAP,iBAAO;IACP,UAAU,EAAV,oBAAU;IACV,SAAS,EAAT,mBAAS;IACT,UAAU,EAAV,oBAAU;IACV,OAAO,EAAP,iBAAO;IACP,IAAI,EAAE,GAAG,EAAE,CAAC,IAAA,gBAAM,GAAE;IACpB,SAAS,EAAT,mBAAS;IACT,MAAM,EAAN,gBAAM;IACN,QAAQ,EAAR,kBAAQ;IACR,SAAS,EAAE,mBAAgC;CAC5C,CAAC;AAEF,SAAgB,oBAAoB;IAClC,MAAM,MAAM,GAAG,IAAA,4BAAoB,EACjC,sBAAY,CAAC,SAAS,EACtB,sBAAY,CAAC,QAAQ,EACrB,sBAAY,CAAC,QAAQ,CACtB,CAAC;IACF,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,sBAAY,CAAC,GAAG,EAAE,CAAC;IACrB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IACb,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { useEffect, useSyncExternalStore } from 'react';\n\nimport {\n  canDismiss,\n  canGoBack,\n  dismiss,\n  dismissAll,\n  dismissTo,\n  goBack,\n  navigate,\n  NavigationOptions,\n  prefetch,\n  push,\n  reload,\n  replace,\n  routingQueue,\n  setParams,\n} from './global-state/routing';\nimport { Href, Route, RouteInputParams } from './types';\n\n/**\n * Returns `router` object for imperative navigation API.\n *\n * @example\n *```tsx\n * import { router } from 'expo-router';\n * import { Text } from 'react-native';\n *\n * export default function Route() {\n *\n *  return (\n *   <Text onPress={() => router.push('/home')}>Go Home</Text>\n *  );\n *}\n * ```\n */\nexport type Router = {\n  /**\n   * Goes back in the navigation history.\n   */\n  back: () => void;\n  /**\n   * Navigates to a route in the navigator's history if it supports invoking the `back` function.\n   */\n  canGoBack: () => boolean;\n  /**\n   * Navigates to the provided [`href`](#href) using a push operation if possible.\n   */\n  push: (href: Href, options?: NavigationOptions) => void;\n  /**\n   * Navigates to the provided [`href`](#href).\n   */\n  navigate: (href: Href, options?: NavigationOptions) => void;\n  /**\n   * Navigates to route without appending to the history. Can be used with\n   * [`useFocusEffect`](#usefocuseffecteffect-do_not_pass_a_second_prop)\n   * to redirect imperatively to a new screen.\n   *\n   * @see [Using `useRouter()` hook](/router/reference/redirects/) to redirect.\n   * */\n  replace: (href: Href, options?: NavigationOptions) => void;\n  /**\n   * Navigates to the a stack lower than the current screen using the provided count if possible, otherwise 1.\n   *\n   * If the current screen is the only route, it will dismiss the entire stack.\n   */\n  dismiss: (count?: number) => void;\n  /**\n   * Dismisses screens until the provided href is reached. If the href is not found, it will instead replace the current screen with the provided `href`.\n   */\n  dismissTo: (href: Href, options?: NavigationOptions) => void;\n  /**\n   * Returns to the first screen in the closest stack. This is similar to\n   * [`popToTop`](https://reactnavigation.org/docs/stack-actions/#poptotop) stack action.\n   */\n  dismissAll: () => void;\n  /**\n   * Checks if it is possible to dismiss the current screen. Returns `true` if the\n   * router is within the stack with more than one screen in stack's history.\n   *\n   */\n  canDismiss: () => boolean;\n  /**\n   * Updates the current route's query params.\n   */\n  setParams: <T extends Route>(params: Partial<RouteInputParams<T>>) => void;\n  /**\n   * Reloads the currently mounted route in experimental server mode. This can be used to re-fetch data.\n   * @hidden\n   */\n  reload: () => void;\n  /**\n   * Prefetch a screen in the background before navigating to it\n   */\n  prefetch: (name: Href) => void;\n};\n\n/**\n * @hidden\n */\nexport const router: Router = {\n  navigate,\n  push,\n  dismiss,\n  dismissAll,\n  dismissTo,\n  canDismiss,\n  replace,\n  back: () => goBack(),\n  canGoBack,\n  reload,\n  prefetch,\n  setParams: setParams as Router['setParams'],\n};\n\nexport function ImperativeApiEmitter() {\n  const events = useSyncExternalStore(\n    routingQueue.subscribe,\n    routingQueue.snapshot,\n    routingQueue.snapshot\n  );\n  useEffect(() => {\n    routingQueue.run();\n  }, [events]);\n  return null;\n}\n"]}