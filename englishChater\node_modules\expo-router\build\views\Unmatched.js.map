{"version": 3, "file": "Unmatched.js", "sourceRoot": "", "sources": ["../../src/views/Unmatched.tsx"], "names": [], "mappings": ";AAAA,mCAAmC;AACnC,YAAY,CAAC;;;;;AAkBb,8BAqHC;AArID,+CAAyC;AACzC,kDAA0B;AAC1B,+CAAuE;AAEvE,oCAAkD;AAClD,uCAAoC;AACpC,oDAAiD;AACjD,kDAA+C;AAE/C,MAAM,eAAe,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,eAAK,CAAC,eAAe,CAAC,CAAC,CAAC,cAAa,CAAC,CAAC;AAE/F;;;;GAIG;AACH,SAAgB,SAAS;IACvB,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,eAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAElD,MAAM,MAAM,GAAG,IAAA,iBAAS,GAAE,CAAC;IAC3B,MAAM,UAAU,GAAG,IAAA,6BAAa,GAAE,CAAC;IACnC,MAAM,QAAQ,GAAG,IAAA,mBAAW,GAAE,CAAC;IAC/B,MAAM,GAAG,GAAG,IAAA,wBAAS,EAAC,QAAQ,CAAC,CAAC;IAEhC,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,SAAS,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,eAAe,CAAC,GAAG,EAAE;QACnB,UAAU,CAAC,UAAU,CAAC;YACpB,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB,OAAO,CACL,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAC5B;MAAA,CAAC,aAAa,CAAC,AAAD,EACd;MAAA,CAAC,mBAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACtD;;MACF,EAAE,mBAAI,CACN;MAAA,CAAC,mBAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CACjF;;MACF,EAAE,mBAAI,CACN;MAAA,CAAC,MAAM,CAAC,CAAC,CAAC,CACR,CAAC,WAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,uBAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAC/E;UAAA,CAAC,qBAAS,CACR;YAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;oBACL,MAAM,CAAC,QAAQ;oBACf,MAAM,CAAC,aAAa;oBACpB,uBAAQ,CAAC,MAAM,CAAC;wBACd,GAAG,EAAE;4BACH,kBAAkB,EAAE,OAAO;4BAC3B,OAAO,EAAE,CAAC;yBACX;qBACF,CAAC;oBACF,OAAO,IAAI;wBACT,OAAO,EAAE,GAAG;wBACZ,kBAAkB,EAAE,WAAW;qBAChC;oBACD,OAAO,IAAI;wBACT,OAAO,EAAE,GAAG;qBACb;iBACF,CAAC,CACF;gBAAA,CAAC,GAAG,CACN;cAAA,EAAE,mBAAI,CAAC,CACR,CACH;UAAA,EAAE,qBAAS,CACb;QAAA,EAAE,WAAI,CAAC,CACR,CAAC,CAAC,CAAC,CACF,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,EAAG,CACvD,CACD;MAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAChC;QAAA,CAAC,qBAAS,CACR;UAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,OAAO,CAAC,CAAC,GAAG,EAAE;gBACZ,IAAI,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC;oBACvB,MAAM,CAAC,IAAI,EAAE,CAAC;gBAChB,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC,CACF,KAAK,CAAC,CAAC;gBACL,MAAM,CAAC,IAAI;gBACX,uBAAQ,CAAC,MAAM,CAAC;oBACd,GAAG,EAAE;wBACH,kBAAkB,EAAE,OAAO;wBAC3B,OAAO,EAAE,CAAC;qBACX;iBACF,CAAC;gBACF,OAAO,IAAI;oBACT,OAAO,EAAE,GAAG;oBACZ,kBAAkB,EAAE,WAAW;iBAChC;gBACD,OAAO,IAAI;oBACT,OAAO,EAAE,GAAG;iBACb;aACF,CAAC,CACF;;YACF,EAAE,mBAAI,CAAC,CACR,CACH;QAAA,EAAE,qBAAS,CACX;QAAA,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,mBAAI,CAClE;QAAA,CAAC,WAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,uBAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAChF;UAAA,CAAC,qBAAS,CACR;YAAA,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CACzB,CAAC,mBAAI,CACH,KAAK,CAAC,CAAC;gBACL,MAAM,CAAC,IAAI;gBACX,uBAAQ,CAAC,MAAM,CAAC;oBACd,GAAG,EAAE;wBACH,kBAAkB,EAAE,OAAO;wBAC3B,OAAO,EAAE,CAAC;qBACX;iBACF,CAAC;gBACF,OAAO,IAAI;oBACT,OAAO,EAAE,GAAG;oBACZ,kBAAkB,EAAE,WAAW;iBAChC;gBACD,OAAO,IAAI;oBACT,OAAO,EAAE,GAAG;iBACb;aACF,CAAC,CACF;;cACF,EAAE,mBAAI,CAAC,CACR,CACH;UAAA,EAAE,qBAAS,CACb;QAAA,EAAE,WAAI,CACR;MAAA,EAAE,mBAAI,CACR;IAAA,EAAE,mBAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAED,SAAS,aAAa;IACpB,OAAO,CAAC,oBAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAG,CAAC;AAC7F,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE;QACT,IAAI,EAAE,CAAC;QACP,eAAe,EAAE,OAAO;QACxB,OAAO,EAAE,EAAE;QACX,aAAa,EAAE,EAAE;QACjB,UAAU,EAAE,QAAQ;QACpB,cAAc,EAAE,QAAQ;KACzB;IACD,KAAK,EAAE;QACL,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,UAAU,EAAE,SAAS;QACrB,YAAY,EAAE,EAAE;KACjB;IACD,KAAK,EAAE;QACL,GAAG,uBAAQ,CAAC,MAAM,CAAC;YACjB,GAAG,EAAE;gBACH,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,EAAE;aACf;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,EAAE;aACf;SACF,CAAC;QACF,KAAK,EAAE,MAAM;QACb,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,QAAQ;KACpB;IACD,QAAQ,EAAE;QACR,QAAQ,EAAE,EAAE;QACZ,SAAS,EAAE,CAAC;QACZ,YAAY,EAAE,EAAE;QAChB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,QAAQ;KACpB;IACD,QAAQ,EAAE;QACR,SAAS,EAAE,EAAE;KACd;IACD,aAAa,EAAE;QACb,KAAK,EAAE,SAAS;KACjB;IACD,WAAW,EAAE;QACX,eAAe,EAAE,WAAW;QAC5B,QAAQ,EAAE,GAAG;QACb,YAAY,EAAE,CAAC;KAChB;IACD,aAAa,EAAE;QACb,SAAS,EAAE,EAAE;QACb,aAAa,EAAE,KAAK;QACpB,GAAG,EAAE,EAAE;KACR;IACD,IAAI,EAAE;QACJ,QAAQ,EAAE,EAAE;QACZ,SAAS,EAAE,QAAQ;QACnB,KAAK,EAAE,SAAS;KACjB;IACD,aAAa,EAAE;QACb,QAAQ,EAAE,EAAE;KACb;CACF,CAAC,CAAC", "sourcesContent": ["// Copyright © 2024 650 Industries.\n'use client';\n\nimport { createURL } from 'expo-linking';\nimport React from 'react';\nimport { StyleSheet, Text, View, Platform, Image } from 'react-native';\n\nimport { usePathname, useRouter } from '../hooks';\nimport { Link } from '../link/Link';\nimport { useNavigation } from '../useNavigation';\nimport { Pressable } from '../views/Pressable';\n\nconst useLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : function () {};\n\n/**\n * Default screen for unmatched routes.\n *\n * @hidden\n */\nexport function Unmatched() {\n  const [render, setRender] = React.useState(false);\n\n  const router = useRouter();\n  const navigation = useNavigation();\n  const pathname = usePathname();\n  const url = createURL(pathname);\n\n  React.useEffect(() => {\n    setRender(true);\n  }, []);\n\n  useLayoutEffect(() => {\n    navigation.setOptions({\n      title: 'Not Found',\n    });\n  }, [navigation]);\n\n  return (\n    <View style={styles.container}>\n      <NotFoundAsset />\n      <Text role=\"heading\" aria-level={1} style={styles.title}>\n        Unmatched Route\n      </Text>\n      <Text role=\"heading\" aria-level={2} style={[styles.subtitle, styles.secondaryText]}>\n        Page could not be found.\n      </Text>\n      {render ? (\n        <Link href={pathname} replace {...Platform.select({ native: { asChild: true } })}>\n          <Pressable>\n            {({ hovered, pressed }) => (\n              <Text\n                style={[\n                  styles.pageLink,\n                  styles.secondaryText,\n                  Platform.select({\n                    web: {\n                      transitionDuration: '200ms',\n                      opacity: 1,\n                    },\n                  }),\n                  hovered && {\n                    opacity: 0.8,\n                    textDecorationLine: 'underline',\n                  },\n                  pressed && {\n                    opacity: 0.8,\n                  },\n                ]}>\n                {url}\n              </Text>\n            )}\n          </Pressable>\n        </Link>\n      ) : (\n        <View style={[styles.pageLink, styles.placeholder]} />\n      )}\n      <View style={styles.linkContainer}>\n        <Pressable>\n          {({ hovered, pressed }) => (\n            <Text\n              onPress={() => {\n                if (router.canGoBack()) {\n                  router.back();\n                } else {\n                  router.replace('/');\n                }\n              }}\n              style={[\n                styles.link,\n                Platform.select({\n                  web: {\n                    transitionDuration: '200ms',\n                    opacity: 1,\n                  },\n                }),\n                hovered && {\n                  opacity: 0.8,\n                  textDecorationLine: 'underline',\n                },\n                pressed && {\n                  opacity: 0.8,\n                },\n              ]}>\n              Go back\n            </Text>\n          )}\n        </Pressable>\n        <Text style={[styles.linkSeparator, styles.secondaryText]}>•</Text>\n        <Link href=\"/_sitemap\" replace {...Platform.select({ native: { asChild: true } })}>\n          <Pressable>\n            {({ hovered, pressed }) => (\n              <Text\n                style={[\n                  styles.link,\n                  Platform.select({\n                    web: {\n                      transitionDuration: '200ms',\n                      opacity: 1,\n                    },\n                  }),\n                  hovered && {\n                    opacity: 0.8,\n                    textDecorationLine: 'underline',\n                  },\n                  pressed && {\n                    opacity: 0.8,\n                  },\n                ]}>\n                Sitemap\n              </Text>\n            )}\n          </Pressable>\n        </Link>\n      </View>\n    </View>\n  );\n}\n\nfunction NotFoundAsset() {\n  return <Image source={require('expo-router/assets/unmatched.png')} style={styles.image} />;\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: 'black',\n    padding: 24,\n    paddingBottom: 64,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  image: {\n    width: 270,\n    height: 168,\n    resizeMode: 'contain',\n    marginBottom: 28,\n  },\n  title: {\n    ...Platform.select({\n      web: {\n        fontSize: 64,\n        lineHeight: 64,\n      },\n      default: {\n        fontSize: 56,\n        lineHeight: 56,\n      },\n    }),\n    color: '#fff',\n    fontWeight: '800',\n    textAlign: 'center',\n  },\n  subtitle: {\n    fontSize: 34,\n    marginTop: 4,\n    marginBottom: 12,\n    fontWeight: '200',\n    textAlign: 'center',\n  },\n  pageLink: {\n    minHeight: 20,\n  },\n  secondaryText: {\n    color: '#9ba1a6',\n  },\n  placeholder: {\n    backgroundColor: '#9ba1a644',\n    minWidth: 180,\n    borderRadius: 5,\n  },\n  linkContainer: {\n    marginTop: 28,\n    flexDirection: 'row',\n    gap: 12,\n  },\n  link: {\n    fontSize: 20,\n    textAlign: 'center',\n    color: '#52a9ff',\n  },\n  linkSeparator: {\n    fontSize: 20,\n  },\n});\n"]}