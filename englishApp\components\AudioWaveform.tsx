import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

interface AudioWaveformProps {
  isRecording: boolean;
  color: string;
}

/**
 * 音频波形组件，显示录音时的动画效果
 */
export function AudioWaveform({ isRecording, color }: AudioWaveformProps) {
  // 创建多个动画值，用于不同的波形线
  const animations = useRef([
    new Animated.Value(0.3),
    new Animated.Value(0.5),
    new Animated.Value(0.7),
    new Animated.Value(0.4),
    new Animated.Value(0.6),
    new Animated.Value(0.5),
    new Animated.Value(0.3),
  ]).current;

  // 当录音状态改变时，启动或停止动画
  useEffect(() => {
    if (isRecording) {
      // 为每个波形线创建循环动画
      const animationSequences = animations.map((anim, index) => {
        // 使用不同的持续时间和延迟，使波形看起来更自然
        const duration = 600 + (index * 100);
        const delay = index * 100;
        
        return Animated.loop(
          Animated.sequence([
            Animated.timing(anim, {
              toValue: Math.random() * 0.7 + 0.3, // 随机高度，在0.3到1.0之间
              duration,
              delay,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: false,
            }),
            Animated.timing(anim, {
              toValue: Math.random() * 0.5 + 0.2, // 随机高度，在0.2到0.7之间
              duration,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: false,
            }),
          ])
        );
      });
      
      // 启动所有动画
      animationSequences.forEach(anim => anim.start());
      
      // 清理函数
      return () => {
        animationSequences.forEach(anim => anim.stop());
      };
    } else {
      // 如果不在录音，重置所有动画值
      animations.forEach((anim, index) => {
        Animated.timing(anim, {
          toValue: 0.1,
          duration: 300,
          useNativeDriver: false,
        }).start();
      });
    }
  }, [isRecording, animations]);

  return (
    <View style={styles.container}>
      {animations.map((anim, index) => (
        <Animated.View
          key={index}
          style={[
            styles.bar,
            {
              backgroundColor: color,
              height: anim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '100%'],
              }),
            },
          ]}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 40,
    width: '100%',
    paddingHorizontal: 20,
  },
  bar: {
    width: 4,
    borderRadius: 2,
    marginHorizontal: 2,
  },
});
