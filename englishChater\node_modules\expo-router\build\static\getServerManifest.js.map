{"version": 3, "file": "getServerManifest.js", "sourceRoot": "", "sources": ["../../src/static/getServerManifest.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;AAeH,0EAgBC;AAGD,kCAgBC;AAhDD,qCAAiC;AACjC,0EAAuE;AACvE,4CAAkD;AAClD,4DAAqF;AACrF,oEAAiE;AAEjE;;;;;;GAMG;AACI,KAAK,UAAU,+BAA+B,CACnD,UAAmB,EAAE;IAErB,MAAM,SAAS,GAAG,IAAA,qBAAS,EAAC,UAAG,EAAE;QAC/B,QAAQ,EAAE,KAAK;QACf,GAAG,OAAO;KACX,CAAC,CAAC;IAEH,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED,6BAA6B;IAC7B,MAAM,IAAA,6CAAqB,EAAC,SAAS,CAAC,CAAC;IAEvC,OAAO,IAAA,qCAAiB,EAAC,SAAS,CAAC,CAAC;AACtC,CAAC;AAED,uDAAuD;AAChD,KAAK,UAAU,WAAW,CAAC,UAAmB,EAAE;IACrD,MAAM,SAAS,GAAG,IAAA,qBAAS,EAAC,UAAG,EAAE;QAC/B,iBAAiB,EAAE,IAAI;QACvB,2BAA2B,EAAE,IAAI;QACjC,QAAQ,EAAE,KAAK;QACf,GAAG,OAAO;KACX,CAAC,CAAC;IAEH,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED,6BAA6B;IAC7B,MAAM,IAAA,6CAAqB,EAAC,SAAS,CAAC,CAAC;IAEvC,OAAO,IAAA,mDAAwB,EAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { ctx } from '../../_ctx';\nimport { getReactNavigationConfig } from '../getReactNavigationConfig';\nimport { getRoutes, Options } from '../getRoutes';\nimport { ExpoRouterServerManifestV1, getServerManifest } from '../getServerManifest';\nimport { loadStaticParamsAsync } from '../loadStaticParamsAsync';\n\n/**\n * Get the server manifest with all dynamic routes loaded with `generateStaticParams`.\n * Unlike the `expo-router/src/routes-manifest.ts` method, this requires loading the entire app in-memory, which\n * takes substantially longer and requires Metro bundling.\n *\n * This is used for the production manifest where we pre-render certain pages and should no longer treat them as dynamic.\n */\nexport async function getBuildTimeServerManifestAsync(\n  options: Options = {}\n): Promise<ExpoRouterServerManifestV1> {\n  const routeTree = getRoutes(ctx, {\n    platform: 'web',\n    ...options,\n  });\n\n  if (!routeTree) {\n    throw new Error('No routes found');\n  }\n\n  // Evaluate all static params\n  await loadStaticParamsAsync(routeTree);\n\n  return getServerManifest(routeTree);\n}\n\n/** Get the linking manifest from a Node.js process. */\nexport async function getManifest(options: Options = {}) {\n  const routeTree = getRoutes(ctx, {\n    preserveApiRoutes: true,\n    preserveRedirectAndRewrites: true,\n    platform: 'web',\n    ...options,\n  });\n\n  if (!routeTree) {\n    throw new Error('No routes found');\n  }\n\n  // Evaluate all static params\n  await loadStaticParamsAsync(routeTree);\n\n  return getReactNavigationConfig(routeTree, false);\n}\n"]}