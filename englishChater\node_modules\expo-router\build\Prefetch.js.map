{"version": 3, "file": "Prefetch.js", "sourceRoot": "", "sources": ["../src/Prefetch.tsx"], "names": [], "mappings": ";;AAaA,4BAUC;AAvBD,iCAAwC;AAExC,qDAA0C;AAC1C,oEAAmE;AAOnE;;GAEG;AACH,SAAgB,QAAQ,CAAC,KAAmB;IAC1C,MAAM,UAAU,GAAG,IAAA,2CAAqB,GAAE,CAAC;IAE3C,IAAA,uBAAe,EAAC,GAAG,EAAE;QACnB,IAAI,UAAU,EAAE,SAAS,EAAE,EAAE,CAAC;YAC5B,uBAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAE7B,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { useLayoutEffect } from 'react';\n\nimport { router } from './imperative-api';\nimport { useOptionalNavigation } from './link/useLoadedNavigation';\nimport { Href } from './types';\n\nexport type PreloadProps = {\n  href: Href;\n};\n\n/**\n * When rendered on a focused screen, this component will preload the specified route.\n */\nexport function Prefetch(props: PreloadProps) {\n  const navigation = useOptionalNavigation();\n\n  useLayoutEffect(() => {\n    if (navigation?.isFocused()) {\n      router.prefetch(props.href);\n    }\n  }, [navigation, props.href]);\n\n  return null;\n}\n"]}