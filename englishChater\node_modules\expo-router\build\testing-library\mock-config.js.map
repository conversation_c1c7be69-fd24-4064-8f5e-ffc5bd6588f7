{"version": 3, "file": "mock-config.js", "sourceRoot": "", "sources": ["../../src/testing-library/mock-config.ts"], "names": [], "mappings": ";;;;;AAsBA,sCAEC;AAED,wCAcC;AAxCD,gDAAwB;AAExB,mDAKyB;AACzB,0DAA0D;AAC1D,4CAA8C;AAa9C,SAAgB,aAAa,CAAC,OAA0B,EAAE,WAAoB,IAAI;IAChF,OAAO,IAAA,sCAAmB,EAAC,IAAA,0BAAc,EAAC,cAAc,CAAC,OAAO,CAAC,CAAE,EAAE,QAAQ,CAAC,CAAC;AACjF,CAAC;AAED,SAAgB,cAAc,CAAC,OAA0B;IACvD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,OAAO,IAAA,8BAAc,EAAC,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;IAC9D,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAClC,OAAO,IAAA,+BAAe,EACpB,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CACnF,CAAC;IACJ,CAAC;SAAM,IAAI,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,EAAE,CAAC;QAClC,OAAO,IAAA,+BAAe,EAAC,OAAO,CAAC,CAAC;IAClC,CAAC;SAAM,IAAI,QAAQ,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;QACrE,OAAO,IAAA,2CAA2B,EAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,SAA0B,CAAC,CAAC;IACzF,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;AACH,CAAC", "sourcesContent": ["import path from 'path';\n\nimport {\n  MemoryContext,\n  inMemoryContext,\n  requireContext,\n  requireContextWithOverrides,\n} from './context-stubs';\nimport { getNavigationConfig } from '../getLinkingConfig';\nimport { getExactRoutes } from '../getRoutes';\n\nexport type MockContextConfig =\n  | string // Pathname to a directory\n  | string[] // Array of filenames to mock as empty components, e.g () => null\n  | MemoryContext // Map of filenames and their exports\n  | {\n      // Directory to load as context\n      appDir: string;\n      // Map of filenames and their exports. Will override contents of files loaded in `appDir\n      overrides: MemoryContext;\n    };\n\nexport function getMockConfig(context: MockContextConfig, metaOnly: boolean = true) {\n  return getNavigationConfig(getExactRoutes(getMockContext(context))!, metaOnly);\n}\n\nexport function getMockContext(context: MockContextConfig) {\n  if (typeof context === 'string') {\n    return requireContext(path.resolve(process.cwd(), context));\n  } else if (Array.isArray(context)) {\n    return inMemoryContext(\n      Object.fromEntries(context.map((filename) => [filename, { default: () => null }]))\n    );\n  } else if (!('appDir' in context)) {\n    return inMemoryContext(context);\n  } else if ('appDir' in context && typeof context.appDir === 'string') {\n    return requireContextWithOverrides(context.appDir, context.overrides as MemoryContext);\n  } else {\n    throw new Error('Invalid context');\n  }\n}\n"]}