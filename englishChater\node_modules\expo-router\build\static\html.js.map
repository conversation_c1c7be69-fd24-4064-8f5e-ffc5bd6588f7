{"version": 3, "file": "html.js", "sourceRoot": "", "sources": ["../../src/static/html.tsx"], "names": [], "mappings": ";;;;;AAWA,oDASC;AAED,oBAYC;AAlCD;;;;;GAKG;AACH,kDAAsD;AAEtD;;GAEG;AACH,SAAgB,oBAAoB;IAClC,OAAO,CACL,CAAC,KAAK,CACJ,EAAE,CAAC,YAAY,CACf,uBAAuB,CAAC,CAAC;YACvB,MAAM,EAAE,sEAAsE;SAC/E,CAAC,EACF,CACH,CAAC;AACJ,CAAC;AAED,SAAgB,IAAI,CAAC,EAAE,QAAQ,EAAqB;IAClD,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CACb;MAAA,CAAC,IAAI,CACH;QAAA,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EACrB;QAAA,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,EACnD;QAAA,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,uDAAuD,EACrF;QAAA,CAAC,oBAAoB,CAAC,AAAD,EACvB;MAAA,EAAE,IAAI,CACN;MAAA,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,IAAI,CACxB;IAAA,EAAE,IAAI,CAAC,CACR,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport React, { type PropsWithChildren } from 'react';\n\n/**\n * Root style-reset for full-screen React Native web apps with a root `<ScrollView />` should use the following styles to ensure native parity. [Learn more](https://necolas.github.io/react-native-web/docs/setup/#root-element).\n */\nexport function ScrollViewStyleReset() {\n  return (\n    <style\n      id=\"expo-reset\"\n      dangerouslySetInnerHTML={{\n        __html: `#root,body,html{height:100%}body{overflow:hidden}#root{display:flex}`,\n      }}\n    />\n  );\n}\n\nexport function Html({ children }: PropsWithChildren) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <meta charSet=\"utf-8\" />\n        <meta httpEquiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\" />\n        <ScrollViewStyleReset />\n      </head>\n      <body>{children}</body>\n    </html>\n  );\n}\n"]}