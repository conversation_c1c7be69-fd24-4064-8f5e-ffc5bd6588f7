{"version": 3, "file": "defineRouter.d.ts", "sourceRoot": "", "sources": ["../../../src/rsc/router/defineRouter.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,EAAkB,iBAAiB,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAW1E,OAAO,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAGvD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,aAAa,EAAY,MAAM,WAAW,CAAC;AACpD,OAAO,KAAK,EAAE,WAAW,EAA+C,MAAM,WAAW,CAAC;AAE1F,KAAK,mBAAmB,GAAG,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,GAAG;IAC5D,QAAQ,EAAE,SAAS,CAAC;CACrB,CAAC;AAEF,KAAK,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAgB7C,wBAAgB,qBAAqB,CACnC,aAAa,EAAE,MAAM,OAAO,CAC1B,QAAQ,CAAC;IACP,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,QAAQ,CAAC;IACf,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB,CAAC,CACH,EACD,YAAY,EAAE,CACZ,WAAW,EAAE,MAAM,EAAE,2BAA2B;AAChD,OAAO,EAAE;IAEP,sBAAsB,EAAE,CAAC,GAAG,CAAC,EAAE,eAAe,KAAK,IAAI,CAAC;IACxD,oBAAoB,EAAE,WAAW,GAAG,SAAS,CAAC;CAC/C,KACE,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,IAAI,CAAC,GAC1F,UAAU,CAAC,OAAO,aAAa,CAAC,CAmKlC;AAED,wBAAgB,iBAAiB,CAC/B,QAAQ,EAAE,MAAM,EAChB,YAAY,CAAC,EAAE,eAAe,EAC9B,IAAI,CAAC,EAAE,MAAM,EAAE,QAUhB"}