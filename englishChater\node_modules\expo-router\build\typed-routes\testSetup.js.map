{"version": 3, "file": "testSetup.js", "sourceRoot": "", "sources": ["../../src/typed-routes/testSetup.ts"], "names": [], "mappings": ";;AAAA,0CAAwC;AACxC,+BAA4B;AAE5B,yCAAiG;AACjG,oEAAkF;AAElF,MAAM,QAAQ,GAGV;IACF,OAAO,EAAE;QACP,OAAO,EAAE;YACP,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI;YACpB,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;YACrB,iBAAiB,EAAE,GAAG,EAAE,CAAC,IAAI;YAC7B,sBAAsB,EAAE,GAAG,EAAE,CAAC,IAAI;YAClC,mCAAmC,EAAE,GAAG,EAAE,CAAC,IAAI;YAC/C,iBAAiB,EAAE,GAAG,EAAE,CAAC,IAAI;YAC7B,6BAA6B,EAAE,GAAG,EAAE,CAAC,IAAI;YACzC,8BAA8B,EAAE,GAAG,EAAE,CAAC,IAAI;YAC1C,iCAAiC,EAAE,GAAG,EAAE,CAAC,IAAI;YAC7C,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI;YAChC,gBAAgB,EAAE,GAAG,EAAE,CAAC,IAAI;SAC7B;KACF;IACD,aAAa,EAAE;QACb,OAAO,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE;QACrC,OAAO,EAAE;YACP,iBAAiB,EAAE,GAAG,EAAE,CAAC,IAAI;YAC7B,6BAA6B,EAAE,GAAG,EAAE,CAAC,IAAI;YACzC,8BAA8B,EAAE,GAAG,EAAE,CAAC,IAAI;YAC1C,iCAAiC,EAAE,GAAG,EAAE,CAAC,IAAI;SAC9C;KACF;CACF,CAAC;AAEF,MAAM,CAAC,OAAO,GAAG;IACf,OAAO,OAAO,CAAC,GAAG,CAChB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAClD,MAAM,QAAQ,GAAG,IAAA,wCAA6B,EAAC,IAAA,+BAAe,EAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAC7E,GAAG,KAAK,CAAC,OAAO;YAChB,kBAAkB,EAAE,IAAI;SACzB,CAAC,CAAC;QAEH,OAAO,IAAA,oBAAS,EAAC,IAAA,WAAI,EAAC,SAAS,EAAE,sBAAsB,EAAE,GAAG,GAAG,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC;IACrF,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { writeFile } from 'fs/promises';\nimport { join } from 'path';\n\nimport { getTypedRoutesDeclarationFile, GetTypedRoutesDeclarationFileOptions } from './generate';\nimport { inMemoryContext, MemoryContext } from '../testing-library/context-stubs';\n\nconst fixtures: Record<\n  string,\n  { context: MemoryContext; options?: GetTypedRoutesDeclarationFileOptions }\n> = {\n  default: {\n    context: {\n      '/apple': () => null,\n      '/banana': () => null,\n      '/colors/[color]': () => null,\n      '/animals/[...animal]': () => null,\n      '/mix/[fruit]/[color]/[...animals]': () => null,\n      '/(group)/static': () => null,\n      '/(group)/(a,b)/folder/index': () => null,\n      '/(group)/(a,b)/folder/[slug]': () => null,\n      '/(group)/(a,b)/folder/[...slug]': () => null,\n      '/(c)/folder/[slug]': () => null,\n      '/(group)/index': () => null,\n    },\n  },\n  partialGroups: {\n    options: { partialTypedGroups: true },\n    context: {\n      '/(group)/static': () => null,\n      '/(group)/(a,b)/folder/index': () => null,\n      '/(group)/(a,b)/folder/[slug]': () => null,\n      '/(group)/(a,b)/folder/[...slug]': () => null,\n    },\n  },\n};\n\nmodule.exports = function () {\n  return Promise.all(\n    Object.entries(fixtures).map(async ([key, value]) => {\n      const template = getTypedRoutesDeclarationFile(inMemoryContext(value.context), {\n        ...value.options,\n        testIgnoreComments: true,\n      });\n\n      return writeFile(join(__dirname, '/__tests__/fixtures/', key + '.d.ts'), template);\n    })\n  );\n};\n"]}