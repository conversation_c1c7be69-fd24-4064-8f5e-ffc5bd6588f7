{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;AAEI,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;AAA1E,QAAA,iBAAiB,qBAAyD;AAEhF,MAAM,WAAW,GAAG,CAAC,KAAa,EAAE,EAAE;IAC3C,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;QACjB,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IACD,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACrD,CAAC;IACD,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACnD,CAAC;IACD,OAAO,KAAK,GAAG,MAAM,CAAC;AACxB,CAAC,CAAC;AAdW,QAAA,WAAW,eActB;AAEF,MAAM,aAAa,GAAG,SAAS,CAAC;AAEzB,MAAM,cAAc,GAAG,CAAC,QAAgB,EAAE,EAAE;IACjD,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAqB,CAAC;IAC7D,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IACD,OAAO,aAAa,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;AAC3C,CAAC,CAAC;AANW,QAAA,cAAc,kBAMzB;AAEK,MAAM,cAAc,GAAG,CAAC,OAAe,EAAE,EAAE;IAChD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACvC,OAAO,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC;AANW,QAAA,cAAc,kBAMzB", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * https://github.com/dai-shi/waku/blob/32d52242c1450b5f5965860e671ff73c42da8bd0/packages/waku/src/client.ts#L1\n */\n\nexport const filePathToFileURL = (filePath: string) => 'file://' + encodeURI(filePath);\n\nexport const encodeInput = (input: string) => {\n  if (input === '') {\n    return 'index.txt';\n  }\n  if (input === 'index') {\n    throw new Error('Input should not be `index`');\n  }\n  if (input.startsWith('/')) {\n    throw new Error('Input should not start with `/`');\n  }\n  if (input.endsWith('/')) {\n    throw new Error('Input should not end with `/`');\n  }\n  return input + '.txt';\n};\n\nconst ACTION_PREFIX = 'ACTION_';\n\nexport const encodeActionId = (actionId: string) => {\n  const [file, name] = actionId.split('#') as [string, string];\n  if (name.includes('/')) {\n    throw new Error('Unsupported action name');\n  }\n  return ACTION_PREFIX + file + '/' + name;\n};\n\nexport const decodeActionId = (encoded: string) => {\n  if (!encoded.startsWith(ACTION_PREFIX)) {\n    return null;\n  }\n  const index = encoded.lastIndexOf('/');\n  return encoded.slice(ACTION_PREFIX.length, index) + '#' + encoded.slice(index + 1);\n};\n"]}