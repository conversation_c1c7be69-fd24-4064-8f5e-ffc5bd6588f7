{"version": 3, "file": "useFocusEffect.js", "sourceRoot": "", "sources": ["../src/useFocusEffect.ts"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8Cb,wCAsGC;AAnJD,gFAAgF;AAChF,sEAAsE;AACtE,6CAA+B;AAE/B,oEAAmE;AAOnE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,SAAgB,cAAc,CAAC,MAAsB,EAAE,yBAAiC;IACtF,MAAM,UAAU,GAAG,IAAA,2CAAqB,GAAE,CAAC;IAE3C,IAAI,yBAAyB,KAAK,SAAS,EAAE,CAAC;QAC5C,MAAM,OAAO,GACX,sFAAsF;YACtF,8EAA8E;YAC9E,mBAAmB;YACnB,+BAA+B;YAC/B,yBAAyB;YACzB,sBAAsB;YACtB,QAAQ;YACR,oEAAoE,CAAC;QAEvE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO;QACT,CAAC;QAED,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,OAAwC,CAAC;QAE7C,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;YAEzB,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;gBAC3D,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC1C,IAAI,OAAO,GACT,6FAA6F,CAAC;gBAEhG,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;oBACrB,OAAO;wBACL,kGAAkG,CAAC;gBACvG,CAAC;qBAAM,IAAI,OAAQ,OAAe,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBACvD,OAAO;wBACL,uFAAuF;4BACvF,uDAAuD;4BACvD,8BAA8B;4BAC9B,mBAAmB;4BACnB,+BAA+B;4BAC/B,oCAAoC;4BACpC,+BAA+B;4BAC/B,uDAAuD;4BACvD,gBAAgB;4BAChB,WAAW;4BACX,oBAAoB;4BACpB,kBAAkB;4BAClB,QAAQ;4BACR,oEAAoE,CAAC;gBACzE,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,kBAAkB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;gBAC3D,CAAC;gBAED,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,CAAC;QAEF,mFAAmF;QACnF,IAAI,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;YAC3B,OAAO,GAAG,QAAQ,EAAE,CAAC;YACrB,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;QAED,MAAM,gBAAgB,GAAG,UAAU,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE;YAC5D,mEAAmE;YACnE,gGAAgG;YAChG,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO;YACT,CAAC;YAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,OAAO,GAAG,QAAQ,EAAE,CAAC;YACrB,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE;YAC1D,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,OAAO,GAAG,SAAS,CAAC;YACpB,SAAS,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,EAAE;YACV,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,gBAAgB,EAAE,CAAC;YACnB,eAAe,EAAE,CAAC;QACpB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;AAC3B,CAAC", "sourcesContent": ["'use client';\n// A fork of `useFocusEffect` that waits for the navigation state to load before\n// running the effect. This is especially useful for native redirects.\nimport * as React from 'react';\n\nimport { useOptionalNavigation } from './link/useLoadedNavigation';\n\n/**\n * Memoized callback containing the effect, should optionally return a cleanup function.\n */\nexport type EffectCallback = () => undefined | void | (() => void);\n\n/**\n * Hook to run an effect whenever a route is **focused**. Similar to\n * [`React.useEffect`](https://react.dev/reference/react/useEffect).\n *\n * This can be used to perform side-effects such as fetching data or subscribing to events.\n * The passed callback should be wrapped in [`React.useCallback`](https://react.dev/reference/react/useCallback)\n * to avoid running the effect too often.\n *\n * @example\n * ```tsx\n * import { useFocusEffect } from 'expo-router';\n * import { useCallback } from 'react';\n *\n * export default function Route() {\n *   useFocusEffect(\n *     // Callback should be wrapped in `React.useCallback` to avoid running the effect too often.\n *     useCallback(() => {\n *       // Invoked whenever the route is focused.\n *       console.log('Hello, I'm focused!');\n *\n *       // Return function is invoked whenever the route gets out of focus.\n *       return () => {\n *         console.log('This route is now unfocused.');\n *       };\n *     }, []);\n *    );\n *\n *  return </>;\n * }\n *```\n *\n * @param effect Memoized callback containing the effect, should optionally return a cleanup function.\n * @param do_not_pass_a_second_prop\n */\nexport function useFocusEffect(effect: EffectCallback, do_not_pass_a_second_prop?: never) {\n  const navigation = useOptionalNavigation();\n\n  if (do_not_pass_a_second_prop !== undefined) {\n    const message =\n      \"You passed a second argument to 'useFocusEffect', but it only accepts one argument. \" +\n      \"If you want to pass a dependency array, you can use 'React.useCallback':\\n\\n\" +\n      'useFocusEffect(\\n' +\n      '  React.useCallback(() => {\\n' +\n      '    // Your code here\\n' +\n      '  }, [depA, depB])\\n' +\n      ');\\n\\n' +\n      'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n\n    console.error(message);\n  }\n\n  React.useEffect(() => {\n    if (!navigation) {\n      return;\n    }\n\n    let isFocused = false;\n    let cleanup: undefined | void | (() => void);\n\n    const callback = () => {\n      const destroy = effect();\n\n      if (destroy === undefined || typeof destroy === 'function') {\n        return destroy;\n      }\n\n      if (process.env.NODE_ENV !== 'production') {\n        let message =\n          'An effect function must not return anything besides a function, which is used for clean-up.';\n\n        if (destroy === null) {\n          message +=\n            \" You returned 'null'. If your effect does not require clean-up, return 'undefined' (or nothing).\";\n        } else if (typeof (destroy as any).then === 'function') {\n          message +=\n            \"\\n\\nIt looks like you wrote 'useFocusEffect(async () => ...)' or returned a Promise. \" +\n            'Instead, write the async function inside your effect ' +\n            'and call it immediately:\\n\\n' +\n            'useFocusEffect(\\n' +\n            '  React.useCallback(() => {\\n' +\n            '    async function fetchData() {\\n' +\n            '      // You can await here\\n' +\n            '      const response = await MyAPI.getData(someId);\\n' +\n            '      // ...\\n' +\n            '    }\\n\\n' +\n            '    fetchData();\\n' +\n            '  }, [someId])\\n' +\n            ');\\n\\n' +\n            'See usage guide: https://reactnavigation.org/docs/use-focus-effect';\n        } else {\n          message += ` You returned '${JSON.stringify(destroy)}'.`;\n        }\n\n        console.error(message);\n      }\n    };\n\n    // We need to run the effect on initial render/dep changes if the screen is focused\n    if (navigation.isFocused()) {\n      cleanup = callback();\n      isFocused = true;\n    }\n\n    const unsubscribeFocus = navigation.addListener('focus', () => {\n      // If callback was already called for focus, avoid calling it again\n      // The focus event may also fire on initial render, so we guard against running the effect twice\n      if (isFocused) {\n        return;\n      }\n\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n\n      cleanup = callback();\n      isFocused = true;\n    });\n\n    const unsubscribeBlur = navigation.addListener('blur', () => {\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n\n      cleanup = undefined;\n      isFocused = false;\n    });\n\n    return () => {\n      if (cleanup !== undefined) {\n        cleanup();\n      }\n\n      unsubscribeFocus();\n      unsubscribeBlur();\n    };\n  }, [effect, navigation]);\n}\n"]}