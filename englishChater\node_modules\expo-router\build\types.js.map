{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["// TODO: Use the global type\n/**\n * @hidden\n */\nexport interface RequireContext {\n  /** Return the keys that can be resolved. */\n  keys(): string[];\n  (id: string): any;\n  <T>(id: string): T;\n  /** **Unimplemented:** Return the module identifier for a user request. */\n  resolve(id: string): string;\n  /** **Unimplemented:** Readable identifier for the context module. */\n  id: string;\n}\n\n/**\n * The list of input keys will become optional, everything else will remain the same.\n */\nexport type PickPartial<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\n\n/**\n * Created by using a special file called `+native-intent.tsx` at the top-level of your\n * project's **app** directory. It exports `redirectSystemPath` or `legacy_subscribe` functions,\n * both methods designed to handle URL/path processing.\n *\n * Useful for re-writing URLs to correctly target a route when unique/referred URLs\n * are incoming from third-party providers or stale URLs from previous versions.\n *\n * @see For more information on how to use `NativeIntent`, see [Customizing links](/router/advanced/native-intent/).\n */\nexport type NativeIntent = {\n  /**\n   * A special method used to process URLs in native apps. When invoked, it receives an\n   * `options` object with the following properties:\n   * - **path**: represents the URL or path undergoing processing.\n   * - **initial**: a boolean indicating whether the path is the app's initial URL.\n   *\n   * It's return value should either be a `string` or a `Promise<string>`.\n   * Note that throwing errors within this method may result in app crashes. It's recommended to\n   * wrap your code inside a `try/catch` block and utilize `.catch()` when appropriate.\n   *\n   * @see For usage information, see [Redirecting system paths](/router/advanced/native-intent/#redirectsystempath).\n   */\n  redirectSystemPath?: (event: { path: string; initial: boolean }) => Promise<string> | string;\n  /**\n   * > **warning** Experimentally available in SDK 52.\n   *\n   * Useful as an alternative API when a third-party provider doesn't support Expo Router\n   * but has support for React Navigation via `Linking.subscribe()` for existing projects.\n   *\n   * Using this API is not recommended for newer projects or integrations since it is\n   * incompatible with Server Side Routing and\n   * [Static Rendering](/router/reference/static-rendering/), and can become challenging to manage while offline or in a low network environment.\n   *\n   */\n  legacy_subscribe?: (listener: (url: string) => void) => undefined | void | (() => void);\n};\n\nexport type * from './typed-routes/types';\n"]}