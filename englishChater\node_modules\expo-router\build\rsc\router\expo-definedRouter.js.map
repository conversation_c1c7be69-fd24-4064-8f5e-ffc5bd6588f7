{"version": 3, "file": "expo-definedRouter.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/expo-definedRouter.ts"], "names": [], "mappings": ";;AAAA,2CAAuC;AAEvC,2DAAsD;AAEtD,qDAA+C;AAC/C,uEAAoE;AACpE,6CAA+C;AAC/C,iDAA8C;AAE9C,MAAM,oBAAoB,GAAG,IAAI,KAAK,CACpC,EAAE,EACF;IACE,qDAAqD;IACrD,GAAG;QACD,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;IAC3E,CAAC;CACF,CACF,CAAC;AAEF,kBAAe,IAAA,mCAAe,EAAC,KAAK,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;IACzF,MAAM,MAAM,GAAG,IAAA,wBAAS,EAAC,UAAG,EAAE;QAC5B,GAAG,eAAe;QAClB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;QAC7B,aAAa,EAAE,IAAI;QACnB,UAAU,EAAE,MAAM;KACnB,CAAC,CAAC;IAEH,IAAI,CAAC,MAAM;QAAE,OAAO;IAEpB,KAAK,UAAU,+BAA+B,CAAC,KAAgB;QAC7D,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,WAAW,GAA2B,SAAS,CAAC;QAEpD,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAqB,EACxC,KAAK,EACL,EAAE,YAAY,EAAE,oBAAoB,EAAE,EACtC,MAAM,CAAC,oBAAoB,CAC5B,CAAC;YAEF,2KAA2K;YAC3K,WAAW,GAAG,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC9B,MAAM,OAAO,GAAa,EAAE,CAAC;gBAE7B,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,OAAQ,EAAE,CAAC;oBACrC,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAChC,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,MAAM,IAAI,KAAK,CACb,yCAAyC;4BACvC,OAAO,CAAC,IAAI;4BACZ,cAAc;4BACd,KAAK,CAAC,UAAU,CACnB,CAAC;oBACJ,CAAC;oBACD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC3B,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACvB,MAAM,IAAI,KAAK,CACb,8IAA8I;gCAC5I,KAAK,CAAC,UAAU,CACnB,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;oBAE5D,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtB,CAAC;gBACD,OAAO,OAAO,CAAC;YACjB,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CACb,2DAA2D,GAAG,KAAK,CAAC,UAAU,CAC/E,CAAC;QACJ,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,KAAK,UAAU,SAAS,CAAC,KAAgB;QACvC,MAAM,MAAM,GAAG,IAAA,wBAAa,EAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAEvE,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CACb,mGAAmG,CACpG,CAAC;QACJ,CAAC;QAED,YAAY,CAAC;YACX,8DAA8D;YAC9D,SAAS,EAAE,MAAM,CAAC,OAAe;YACjC,IAAI,EAAE,MAAa;YACnB,MAAM,EAAE,QAAQ;YAChB,GAAG,MAAM,CAAC,iBAAiB;SAC5B,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAU,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAClD,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,MAAM,SAAS,CAAC,KAAK,CAAC,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAAG,IAAA,wBAAa,EAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBACvE,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,iBAAiB,CAAC;gBAE1C,+EAA+E;gBAC/E,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;oBAChC,UAAU,CAAC;wBACT,8DAA8D;wBAC9D,SAAS,EAAE,MAAM,CAAC,OAAc;wBAChC,IAAI,EAAE,MAAa;wBACnB,MAAM,EAAE,QAAQ;wBAChB,GAAG,MAAM,CAAC,iBAAiB;wBAC3B,WAAW,EAAE,CAAC,MAAM,+BAA+B,CAAC,KAAK,CAAC,CAAQ;qBACnE,CAAC,CAAC;oBAEH,IAAI,QAAQ,EAAE,MAAM,KAAK,QAAQ,EAAE,CAAC;wBAClC,UAAU,CAAC;4BACT,8DAA8D;4BAC9D,SAAS,EAAE,MAAM,CAAC,OAAc;4BAChC,IAAI,EAAE,MAAa;4BACnB,MAAM,EAAE,SAAS;4BACjB,GAAG,QAAQ;yBACZ,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC;wBACT,8DAA8D;wBAC9D,SAAS,EAAE,MAAM,CAAC,OAAc;wBAChC,IAAI,EAAE,MAAa;wBACnB,MAAM,EAAE,SAAS;wBACjB,GAAG,QAAQ;qBACZ,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,MAAM,SAAS,CAAC,MAAM,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC", "sourcesContent": ["import { ctx } from 'expo-router/_ctx';\n\nimport { createExpoPages } from './create-expo-pages';\nimport type { RouteNode } from '../../Route';\nimport { getRoutes } from '../../getRoutesSSR';\nimport { evalStaticParamsAsync } from '../../loadStaticParamsAsync';\nimport { getContextKey } from '../../matchers';\nimport { sortRoutes } from '../../sortRoutes';\n\nconst UNIMPLEMENTED_PARAMS = new Proxy(\n  {},\n  {\n    // Assert that params is unimplemented when accessed.\n    get() {\n      throw new Error('generateStaticParams(): params is not implemented yet');\n    },\n  }\n);\n\nexport default createExpoPages(async ({ createPage, createLayout }, { getRouteOptions }) => {\n  const routes = getRoutes(ctx, {\n    ...getRouteOptions,\n    platform: process.env.EXPO_OS,\n    skipGenerated: true,\n    importMode: 'lazy',\n  });\n\n  if (!routes) return;\n\n  async function loadAndConvertStaticParamsAsync(route: RouteNode) {\n    const loaded = route.loadRoute();\n\n    let staticPaths: string[][] | undefined = undefined;\n\n    if (route.dynamic) {\n      const params = await evalStaticParamsAsync(\n        route,\n        { parentParams: UNIMPLEMENTED_PARAMS },\n        loaded.generateStaticParams\n      );\n\n      // Sort `params` like `[{a: 'x', b: 'y'}, { a: 'z', b: 'w' }]` for a route.dynamic like `[{name: 'a', deep: false}, {name: 'b', deep: false}]` to `[['a', 'y'], ['z', 'w]]`\n      staticPaths = params?.map((p) => {\n        const grouped: string[] = [];\n\n        for (const dynamic of route.dynamic!) {\n          const defined = p[dynamic.name];\n          if (!defined) {\n            throw new Error(\n              'generateStaticParams is missing param: ' +\n                dynamic.name +\n                '. In route: ' +\n                route.contextKey\n            );\n          }\n          if (Array.isArray(defined)) {\n            if (defined.length > 1) {\n              throw new Error(\n                'generateStaticParams does not support returning multiple static paths for deep dynamic routes in React Server Components yet. Update route: ' +\n                  route.contextKey\n              );\n            }\n          }\n\n          const first = Array.isArray(defined) ? defined[0] : defined;\n\n          grouped.push(first);\n        }\n        return grouped;\n      });\n    } else if (loaded.generateStaticParams) {\n      throw new Error(\n        'Cannot use generateStaticParams without a dynamic route: ' + route.contextKey\n      );\n    }\n    return staticPaths;\n  }\n  async function addLayout(route: RouteNode) {\n    const normal = getContextKey(route.contextKey).replace(/\\/index$/, '');\n\n    const loaded = route.loadRoute();\n\n    if (loaded.generateStaticParams) {\n      throw new Error(\n        'generateStaticParams is not supported in _layout routes with React Server Components enabled yet.'\n      );\n    }\n\n    createLayout({\n      // NOTE(EvanBacon): Support routes with top-level \"use client\"\n      component: loaded.default! as any,\n      path: normal as any,\n      render: 'static',\n      ...loaded.unstable_settings,\n    });\n\n    await Promise.all(\n      route.children.sort(sortRoutes).map(async (child) => {\n        if (child.type === 'layout') {\n          await addLayout(child);\n        } else {\n          const normal = getContextKey(child.contextKey).replace(/\\/index$/, '');\n          const loaded = child.loadRoute();\n          const settings = loaded.unstable_settings;\n\n          // Support generateStaticParams for dynamic routes by defining the route twice.\n          if (loaded.generateStaticParams) {\n            createPage({\n              // NOTE(EvanBacon): Support routes with top-level \"use client\"\n              component: loaded.default as any,\n              path: normal as any,\n              render: 'static',\n              ...loaded.unstable_settings,\n              staticPaths: (await loadAndConvertStaticParamsAsync(child)) as any,\n            });\n\n            if (settings?.render !== 'static') {\n              createPage({\n                // NOTE(EvanBacon): Support routes with top-level \"use client\"\n                component: loaded.default as any,\n                path: normal as any,\n                render: 'dynamic',\n                ...settings,\n              });\n            }\n          } else {\n            createPage({\n              // NOTE(EvanBacon): Support routes with top-level \"use client\"\n              component: loaded.default as any,\n              path: normal as any,\n              render: 'dynamic',\n              ...settings,\n            });\n          }\n        }\n      })\n    );\n  }\n\n  await addLayout(routes);\n});\n"]}