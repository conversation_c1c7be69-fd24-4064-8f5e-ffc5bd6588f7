{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../src/ui/common.tsx"], "names": [], "mappings": ";;;AA8CA,8CAgJC;AAED,sCAyCC;AAnOD,uCAA0E;AAC1E,8CAAsD;AAEtD,8CAA8C;AAC9C,iCAA8B;AAE9B,mFAAmF;AACtE,QAAA,QAAQ,GAAG,WAEvB,CAAC;AAEW,QAAA,gBAAgB,GAAG,WAE/B,CAAC;AA2BF,SAAgB,iBAAiB,CAC/B,QAAyB,EACzB,eAA0B,EAC1B,OAAsC,EACtC,gBAAoC,EACpC,gBAA4B,EAC5B,SAAoB,EACpB,UAAkB;IAElB,MAAM,OAAO,GAAoB,EAAE,CAAC;IAEpC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,CAAC,IAAI,IAAI,gBAAgB,EAAE,CAAC;YACrC,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,IAAI,KAAK,CACb,WAAW,IAAI,CAAC,SAAS,CAAC;gBACxB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAC,wCAAwC,IAAI,CAAC,SAAS,CAAC;gBACvD,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,IAAI,EAAE,aAAa,CAAC,IAAI;aACzB,CAAC,oCAAoC,CACvC,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,SAAS;QACX,CAAC;QAED,IAAI,YAAY,GAAG,IAAA,kBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,qBAAqB,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YACrE,OAAO,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,YAAY,GAAG,IAAA,oCAA6B,EAC1C,YAAY,EACZ;YACE,GAAG,SAAS;YACZ,QAAQ,EAAE,qBAAqB;SAChC,EACD,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAC9B,CAAC;QAEF,IAAI,KAAK,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,gEAAgE;YAChE,OAAO,CAAC,IAAI,CACV,qCAAqC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,sCAAsC,CACnG,CAAC;YACF,SAAS;QACX,CAAC;QAED,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,MAAM,eAAe,GAAG,eAAe,CAAC,KAAK,IAAI,QAAQ,CAAC;QAE1D,+DAA+D;QAC/D,sDAAsD;QACtD,OAAO,KAAK,EAAE,KAAK,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe;gBAAE,MAAM;YAC1C,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjF,CAAC;QACD,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC;QAE9F,MAAM,SAAS,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,CAAC;QAE7F,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CACV,wCAAwC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,wCAAwC,CACxG,CAAC;YACF,SAAS;QACX,CAAC;QAED,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACxF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC1C,OAAO,CAAC,IAAI,CACV,gBAAgB,OAAO,CAAC,IAAI,mBAAmB,OAAO,CAAC,IAAI,uCAAuC,CACnG,CAAC;YACJ,CAAC;YACD,SAAS;QACX,CAAC;QAED,MAAM,gBAAgB,GACpB,OAAO,CAAC,IAAI,KAAK,UAAU;YAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAA0D,EAAE;gBAC9E,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC/B,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;YACpD,CAAC,CAAC,CAAC;QAEL,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,oBAAoB,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,gBAAgB,CAAC,IAAI,EAAE,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;YAEjL,MAAM,IAAI,KAAK,CACb,2LAA2L,oBAAoB,iCAAiC,SAAS,CAAC,KAAK,GAAG,CACnQ,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,IAAI,CAAC;YACX,GAAG,OAAO;YACV,IAAI,EAAE,YAAY;YAClB,SAAS;YACT,MAAM,EAAE,aAAa,CAAC,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC;SACpD,CAAC,CAAC;IACL,CAAC;IAED,MAAM,MAAM,GAAG,IAAA,kCAAqB,EAAC,gBAAgB,CAAC,CAAC;IAEvD,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1C,kEAAkE;QAClE,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACnD,OAAO,CAAC,CAAC;QACX,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,CAAC;QACX,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAwB,EAAE,CAAC;IACzC,MAAM,UAAU,GAAe,EAAE,GAAG,gBAAgB,EAAE,CAAC;IAEvD,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;QACtD,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,CAAC;QAE/C,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,QAAQ,CAAC,IAAI,CAAC,IAAA,0BAAa,EAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IACD,OAAO;QACL,QAAQ;QACR,UAAU;KACX,CAAC;AACJ,CAAC;AAED,SAAgB,aAAa,CAC3B,KAAkE,EAClE,YAAqB;IAErB,MAAM,WAAW,GAAQ,EAAE,CAAC;IAC5B,IAAI,OAAO,GAAG,WAAW,CAAC;IAE1B,YAAY,GAAG,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC;IAE7D,IAAI,kBAAkB,GAAG,YAAY,KAAK,SAAS,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC;IAErE,OAAO,KAAK,EAAE,CAAC;QACb,IAAI,kBAAkB,EAAE,CAAC;YACvB,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;YAC9B,CAAC;YACD,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAEzD,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAE5D,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;gBACtB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAC3B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAChC,kBAAkB,GAAG,IAAI,CAAC;YAC5B,CAAC;YACD,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtE,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,GAAG,SAAS,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,WAAW;KACrB,CAAC;AACJ,CAAC", "sourcesContent": ["import { LinkingOptions, ParamListBase, PartialRoute, Route } from '@react-navigation/native';\nimport { ViewProps, View, SafeAreaView } from 'react-native';\n\nimport type { ExpoTabActionType } from './TabRouter';\nimport { UrlObject } from '../LocationProvider';\nimport { RouteNode } from '../Route';\nimport { resolveHref, resolveHrefStringWithSegments } from '../link/href';\nimport { sortRoutesWithInitial } from '../sortRoutes';\nimport { Href } from '../types';\nimport { routeToScreen } from '../useScreens';\nimport { Slot } from './Slot';\n\n// Fix the TypeScript types for <Slot />. It complains about the ViewProps[\"style\"]\nexport const ViewSlot = Slot as React.ForwardRefExoticComponent<\n  ViewProps & React.RefAttributes<View>\n>;\n\nexport const SafeAreaViewSlot = Slot as React.ForwardRefExoticComponent<\n  ViewProps & React.RefAttributes<SafeAreaView>\n>;\n\nexport type ScreenTrigger =\n  | {\n      type: 'internal';\n      href: Href;\n      name: string;\n    }\n  | {\n      type: 'external';\n      name: string;\n      href: string;\n    };\n\ntype JumpToNavigationAction = Extract<ExpoTabActionType, { type: 'JUMP_TO' }>;\ntype TriggerConfig =\n  | {\n      type: 'internal';\n      name: string;\n      href: string;\n      routeNode: RouteNode;\n      action: JumpToNavigationAction;\n    }\n  | { type: 'external'; name: string; href: string };\n\nexport type TriggerMap = Record<string, TriggerConfig & { index: number }>;\n\nexport function triggersToScreens(\n  triggers: ScreenTrigger[],\n  layoutRouteNode: RouteNode,\n  linking: LinkingOptions<ParamListBase>,\n  initialRouteName: undefined | string,\n  parentTriggerMap: TriggerMap,\n  routeInfo: UrlObject,\n  contextKey: string\n) {\n  const configs: TriggerConfig[] = [];\n\n  for (const trigger of triggers) {\n    if (trigger.name in parentTriggerMap) {\n      const parentTrigger = parentTriggerMap[trigger.name];\n      throw new Error(\n        `Trigger ${JSON.stringify({\n          name: trigger.name,\n          href: trigger.href,\n        })} has the same name as parent trigger ${JSON.stringify({\n          name: parentTrigger.name,\n          href: parentTrigger.href,\n        })}. Triggers must have unique names.`\n      );\n    }\n\n    if (trigger.type === 'external') {\n      configs.push(trigger);\n      continue;\n    }\n\n    let resolvedHref = resolveHref(trigger.href);\n\n    if (resolvedHref.startsWith('../')) {\n      throw new Error('Trigger href cannot link to a parent directory');\n    }\n\n    const segmentsWithoutGroups = contextKey.split('/').filter((segment) => {\n      return !(segment.startsWith('(') && segment.endsWith(')'));\n    });\n\n    resolvedHref = resolveHrefStringWithSegments(\n      resolvedHref,\n      {\n        ...routeInfo,\n        segments: segmentsWithoutGroups,\n      },\n      { relativeToDirectory: true }\n    );\n\n    let state = linking.getStateFromPath?.(resolvedHref, linking.config)?.routes[0];\n\n    if (!state) {\n      // This shouldn't occur, as you should get the global +not-found\n      console.warn(\n        `Unable to find screen for trigger ${JSON.stringify(trigger)}. Does this point to a valid screen?`\n      );\n      continue;\n    }\n\n    let routeState = state;\n\n    const targetStateName = layoutRouteNode.route || '__root';\n\n    // The state object is the current state from the rootNavigator\n    // We need to work out the state for just this trigger\n    while (state?.state) {\n      if (state.name === targetStateName) break;\n      state = state.state.routes[state.state.index ?? state.state.routes.length - 1];\n    }\n    routeState = state.state?.routes[state.state.index ?? state.state.routes.length - 1] || state;\n\n    const routeNode = layoutRouteNode.children.find((child) => child.route === routeState?.name);\n\n    if (!routeNode) {\n      console.warn(\n        `Unable to find routeNode for trigger ${JSON.stringify(trigger)}. This might be a bug with Expo Router`\n      );\n      continue;\n    }\n\n    if (routeNode.generated && routeNode.internal && routeNode.route.includes('+not-found')) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn(\n          `Tab trigger '${trigger.name}' has the href '${trigger.href}' which points to a +not-found route.`\n        );\n      }\n      continue;\n    }\n\n    const duplicateTrigger =\n      trigger.type === 'internal' &&\n      configs.find((config): config is Extract<TriggerConfig, { type: 'internal' }> => {\n        if (config.type === 'external') {\n          return false;\n        }\n\n        return config.routeNode.route === routeNode.route;\n      });\n\n    if (duplicateTrigger) {\n      const duplicateTriggerText = `${JSON.stringify({ name: duplicateTrigger.name, href: duplicateTrigger.href })} and ${JSON.stringify({ name: trigger.name, href: trigger.href })}`;\n\n      throw new Error(\n        `A navigator cannot contain multiple trigger components that map to the same sub-segment. Consider adding a shared group and assigning a group to each trigger. Conflicting triggers:\\n\\t${duplicateTriggerText}.\\nBoth triggers map to route ${routeNode.route}.`\n      );\n    }\n\n    configs.push({\n      ...trigger,\n      href: resolvedHref,\n      routeNode,\n      action: stateToAction(state, layoutRouteNode.route),\n    });\n  }\n\n  const sortFn = sortRoutesWithInitial(initialRouteName);\n\n  const sortedConfigs = configs.sort((a, b) => {\n    // External routes should be last. They will eventually be dropped\n    if (a.type === 'external' && b.type === 'external') {\n      return 0;\n    } else if (a.type === 'external') {\n      return 1;\n    } else if (b.type === 'external') {\n      return -1;\n    }\n\n    return sortFn(a.routeNode, b.routeNode);\n  });\n\n  const children: React.JSX.Element[] = [];\n  const triggerMap: TriggerMap = { ...parentTriggerMap };\n\n  for (const [index, config] of sortedConfigs.entries()) {\n    triggerMap[config.name] = { ...config, index };\n\n    if (config.type === 'internal') {\n      children.push(routeToScreen(config.routeNode));\n    }\n  }\n  return {\n    children,\n    triggerMap,\n  };\n}\n\nexport function stateToAction(\n  state: PartialRoute<Route<string, object | undefined>> | undefined,\n  startAtRoute?: string\n): JumpToNavigationAction {\n  const rootPayload: any = {};\n  let payload = rootPayload;\n\n  startAtRoute = startAtRoute === '' ? '__root' : startAtRoute;\n\n  let foundStartingPoint = startAtRoute === undefined || !state?.state;\n\n  while (state) {\n    if (foundStartingPoint) {\n      if (payload === rootPayload) {\n        payload.name = state.name;\n      } else {\n        payload.screen = state.name;\n      }\n      payload.params = state.params ? { ...state.params } : {};\n\n      state = state.state?.routes[state.state?.routes.length - 1];\n\n      if (state) {\n        payload.params ??= {};\n        payload = payload.params;\n      }\n    } else {\n      if (state.name === startAtRoute) {\n        foundStartingPoint = true;\n      }\n      const nextState = state.state?.routes[state.state?.routes.length - 1];\n      if (nextState) {\n        state = nextState;\n      }\n    }\n  }\n\n  return {\n    type: 'JUMP_TO',\n    payload: rootPayload,\n  };\n}\n"]}