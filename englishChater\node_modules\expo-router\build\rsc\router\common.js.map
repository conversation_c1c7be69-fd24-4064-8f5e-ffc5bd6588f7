{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/common.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAQH,0CASC;AAED,wCAKC;AAED,4CAEC;AApBD,SAAgB,eAAe,CAAC,IAAY;IAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAClD,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAC;IAChC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;QACvD,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC;IACD,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,SAAgB,cAAc,CAAC,IAAY;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;AAED,SAAgB,gBAAgB,CAAC,KAAa;IAC5C,OAAO,GAAG,GAAG,KAAK,CAAC;AACrB,CAAC;AAEY,QAAA,cAAc,GAAG,kBAAkB,CAAC;AAEjD,oEAAoE;AACvD,QAAA,cAAc,GAAG,cAAc,CAAC;AAE7C,qEAAqE;AACxD,QAAA,WAAW,GAAG,WAAW,CAAC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nexport type RouteProps = {\n  path: string;\n  query: string;\n  hash: string;\n};\n\nexport function getComponentIds(path: string): readonly string[] {\n  const pathItems = path.split('/').filter(Boolean);\n  const idSet = new Set<string>();\n  for (let index = 0; index <= pathItems.length; ++index) {\n    const id = [...pathItems.slice(0, index), 'layout'].join('/');\n    idSet.add(id);\n  }\n  idSet.add([...pathItems, 'page'].join('/'));\n  return Array.from(idSet);\n}\n\nexport function getInputString(path: string): string {\n  if (!path.startsWith('/')) {\n    throw new Error('Path should start with `/`');\n  }\n  return path.slice(1);\n}\n\nexport function parseInputString(input: string): string {\n  return '/' + input;\n}\n\nexport const PARAM_KEY_SKIP = 'expo_router_skip';\n\n// It starts with \"/\" to avoid conflicing with normal component ids.\nexport const SHOULD_SKIP_ID = '/SHOULD_SKIP';\n\n// It starts with \"/\" to avoid conflicting with normal component ids.\nexport const LOCATION_ID = '/LOCATION';\n\n// TODO revisit shouldSkip API\nexport type ShouldSkip = (readonly [\n  componentId: string,\n  components: readonly [\n    path?: boolean, // if we compare path\n    keys?: string[], // searchParams keys to compare\n  ],\n])[];\n"]}