{"version": 3, "file": "useNavigation.js", "sourceRoot": "", "sources": ["../src/useNavigation.ts"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;AAgEb,sCAsFC;AArJD,qDAKkC;AAElC,2CAAiD;AACjD,sCAA0C;AAG1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmDG;AACH,SAAgB,aAAa,CAI3B,MAAsB;IACtB,IAAI,UAAU,GAAG,IAAA,sBAAqB,GAAO,CAAC;IAC9C,IAAI,KAAK,GAAG,IAAA,wBAAe,GAAE,CAAC;IAE9B,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,iEAAiE;QACjE,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,uEAAuE;IACvE,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;IACrC,IAAI,SAAS,KAAK,EAAE,IAAI,SAAS,KAAK,oCAAoC,EAAE,CAAC;QAC3E,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,MAAM,GAAG,IAAA,kBAAW,EAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;QACnB,6BAA6B;QAC7B,OAAO,UAAU,CAAC,SAAS,CAAC,oCAAoC,CAAC,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAChG,CAAC;SAAM,IAAI,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACrC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACpB,4DAA4D;YAC5D,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,uDAAuD;QACvD,MAAM,cAAc,GAAG,MAAM,CAAC;QAC9B,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACzB,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CACb,wBAAwB,cAAc,+DAA+D,CACtG,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC;QAExC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CACb,wBAAwB,cAAc,mBAAmB,MAAM,iCAAiC,CACjG,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEtB,iFAAiF;QACjF,wFAAwF;QACxF,IAAI,MAAM,IAAI,MAAM,KAAK,8BAAkB,EAAE,CAAC;YAC5C,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAE1C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,GAAG,GAA2B,EAAE,CAAC;YACvC,OAAO,UAAU,EAAE,CAAC;gBAClB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC;gBACpC,UAAU,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;YACtC,CAAC;YAED,MAAM,IAAI,KAAK,CACb,gDAAgD,MAAM,6BAA6B,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CACvG,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC", "sourcesContent": ["'use client';\nimport {\n  useNavigation as useUpstreamNavigation,\n  NavigationProp,\n  NavigationState,\n  useStateForPath,\n} from '@react-navigation/native';\n\nimport { INTERNAL_SLOT_NAME } from './constants';\nimport { resolveHref } from './link/href';\nimport { Href } from './types';\n\n/**\n * Returns the underlying React Navigation [`navigation` object](https://reactnavigation.org/docs/navigation-object)\n * to imperatively access layout-specific functionality like `navigation.openDrawer()` in a\n * [Drawer](/router/advanced/drawer/) layout.\n *\n * @example\n * ```tsx app/index.tsx\n * import { useNavigation } from 'expo-router';\n *\n * export default function Route() {\n *   // Access the current navigation object for the current route.\n *   const navigation = useNavigation();\n *\n *   return (\n *     <View>\n *       <Text onPress={() => {\n *         // Open the drawer view.\n *         navigation.openDrawer();\n *       }}>\n *         Open Drawer\n *       </Text>\n *     </View>\n *   );\n * }\n * ```\n *\n * When using nested layouts, you can access higher-order layouts by passing a secondary argument denoting the layout route.\n * For example, `/menu/_layout.tsx` is nested inside `/app/orders/`, you can use `useNavigation('/orders/menu/')`.\n *\n * @example\n * ```tsx app/orders/menu/index.tsx\n * import { useNavigation } from 'expo-router';\n *\n * export default function MenuRoute() {\n *   const rootLayout = useNavigation('/');\n *   const ordersLayout = useNavigation('/orders');\n *\n *   // Same as the default results of `useNavigation()` when invoked in this route.\n *   const parentLayout = useNavigation('/orders/menu');\n * }\n * ```\n *\n * If you attempt to access a layout that doesn't exist, an error such as\n * `Could not find parent navigation with route \"/non-existent\"` is thrown.\n *\n *\n * @param parent Provide an absolute path such as `/(root)` to the parent route or a relative path like `../../` to the parent route.\n * @returns The navigation object for the current route.\n *\n * @see React Navigation documentation on [navigation dependent functions](https://reactnavigation.org/docs/navigation-object/#navigator-dependent-functions)\n * for more information.\n */\nexport function useNavigation<\n  T = Omit<NavigationProp<ReactNavigation.RootParamList>, 'getState'> & {\n    getState(): NavigationState | undefined;\n  },\n>(parent?: string | Href): T {\n  let navigation = useUpstreamNavigation<any>();\n  let state = useStateForPath();\n\n  if (parent === undefined) {\n    // If no parent is provided, return the current navigation object\n    return navigation;\n  }\n\n  // Check for the top-level navigator - we cannot fetch anything higher!\n  const currentId = navigation.getId();\n  if (currentId === '' || currentId === `/expo-router/build/views/Navigator`) {\n    return navigation;\n  }\n\n  if (typeof parent === 'object') {\n    parent = resolveHref(parent);\n  }\n\n  if (parent === '/') {\n    // This is the root navigator\n    return navigation.getParent(`/expo-router/build/views/Navigator`) ?? navigation.getParent(``);\n  } else if (parent?.startsWith('../')) {\n    const names: string[] = [];\n\n    while (state) {\n      const route = state.routes[0];\n      state = route.state;\n      // Don't include the last router, as thats the current route\n      if (state) {\n        names.push(route.name);\n      }\n    }\n\n    // Removing the trailing slash to make splitting easier\n    const originalParent = parent;\n    if (parent.endsWith('/')) {\n      parent = parent.slice(0, -1);\n    }\n\n    const segments = parent.split('/');\n    if (!segments.every((segment) => segment === '..')) {\n      throw new Error(\n        `Invalid parent path \"${originalParent}\". Only \"../\" segments are allowed when using relative paths.`\n      );\n    }\n\n    const levels = segments.length;\n    const index = names.length - 1 - levels;\n\n    if (index < 0) {\n      throw new Error(\n        `Invalid parent path \"${originalParent}\". Cannot go up ${levels} levels from the current route.`\n      );\n    }\n\n    parent = names[index];\n\n    // Expo Router navigators use the context key as the name which has a leading `/`\n    // The exception to this is the INTERNAL_SLOT_NAME, and the root navigator which uses ''\n    if (parent && parent !== INTERNAL_SLOT_NAME) {\n      parent = `/${parent}`;\n    }\n  }\n\n  navigation = navigation.getParent(parent);\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (!navigation) {\n      const ids: (string | undefined)[] = [];\n      while (navigation) {\n        ids.push(navigation.getId() || '/');\n        navigation = navigation.getParent();\n      }\n\n      throw new Error(\n        `Could not find parent navigation with route \"${parent}\". Available routes are: '${ids.join(\"', '\")}'`\n      );\n    }\n  }\n\n  return navigation;\n}\n"]}