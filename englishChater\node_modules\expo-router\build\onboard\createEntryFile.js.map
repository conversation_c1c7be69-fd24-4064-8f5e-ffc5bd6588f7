{"version": 3, "file": "createEntryFile.js", "sourceRoot": "", "sources": ["../../src/onboard/createEntryFile.ts"], "names": [], "mappings": ";;AAGA,oDAkBC;AAED,0CAEC;AAzBD,kDAA+C;AAE/C,4DAA4D;AAC5D,SAAgB,oBAAoB;IAClC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,gBAAgB;QAChB,OAAO,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAC/D,OAAO;IACT,CAAC;IAED,+CAA+C;IAC/C,OAAO,KAAK,CAAC,IAAA,2BAAY,GAAE,CAAC,GAAG,GAAG,aAAa,EAAE;QAC/C,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;YACnB,QAAQ,EAAE,QAAQ;YAClB,SAAS;YACT,IAAI,EAAE,gBAAgB;YACtB,MAAM;YACN,YAAY,EAAE,eAAe,EAAE;SAChC,CAAC;KACH,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,eAAe;IAC7B,OAAO,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,WAAW,CAAC;AAC5D,CAAC;AAED,MAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkChB,CAAC", "sourcesContent": ["import { getDevServer } from '../getDevServer';\n\n/** Middleware for creating an entry file in the project. */\nexport function createEntryFileAsync() {\n  if (process.env.NODE_ENV === 'production') {\n    // No dev server\n    console.warn('createEntryFile() cannot be used in production');\n    return;\n  }\n\n  // Pings middleware in the Expo CLI dev server.\n  return fetch(getDevServer().url + '_expo/touch', {\n    method: 'POST',\n    body: JSON.stringify({\n      contents: TEMPLATE,\n      // Legacy\n      path: './app/index.js',\n      // New\n      absolutePath: getAbsolutePath(),\n    }),\n  });\n}\n\nexport function getAbsolutePath() {\n  return process.env.EXPO_ROUTER_ABS_APP_ROOT + '/index.js';\n}\n\nconst TEMPLATE = `import { StyleSheet, Text, View } from \"react-native\";\n\nexport default function Page() {\n  return (\n    <View style={styles.container}>\n      <View style={styles.main}>\n        <Text style={styles.title}>Hello World</Text>\n        <Text style={styles.subtitle}>This is the first page of your app.</Text>\n      </View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    alignItems: \"center\",\n    padding: 24,\n  },\n  main: {\n    flex: 1,\n    justifyContent: \"center\",\n    maxWidth: 960,\n    marginHorizontal: \"auto\",\n  },\n  title: {\n    fontSize: 64,\n    fontWeight: \"bold\",\n  },\n  subtitle: {\n    fontSize: 36,\n    color: \"#38434D\",\n  },\n});\n`;\n"]}