{"version": 3, "file": "loadStaticParamsAsync.js", "sourceRoot": "", "sources": ["../src/loadStaticParamsAsync.ts"], "names": [], "mappings": ";;AAEA,sDAMC;AAED,sDAyBC;AAuHD,gDA6DC;AArNM,KAAK,UAAU,qBAAqB,CAAC,KAAgB;IAC1D,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,GAAG,CACxC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,yBAAyB,CAAC,KAAK,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC,CACtF,CAAC;IACF,KAAK,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,EAAE,CAAC;IACzC,OAAO,KAAK,CAAC;AACf,CAAC;AAEM,KAAK,UAAU,qBAAqB,CACzC,KAAgB,EAChB,KAA4B,EAC5B,oBAEyC;IAEzC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,oBAAoB,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CACb,uEAAuE,GAAG,KAAK,CAAC,UAAU,CAC3F,CAAC;IACJ,CAAC;IAED,IAAI,oBAAoB,EAAE,CAAC;QACzB,MAAM,YAAY,GAAG,MAAM,oBAAoB,CAAC;YAC9C,MAAM,EAAE,KAAK,CAAC,YAAY,IAAI,EAAE;SACjC,CAAC,CAAC;QAEH,sBAAsB,CAAC,YAAY,CAAC,CAAC;QACrC,sEAAsE;QACtE,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QAEpE,OAAO,YAAY,CAAC;IACtB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,KAAK,UAAU,yBAAyB,CACtC,KAAgB,EAChB,KAA4B;IAE5B,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;QAChD,OAAO,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC;IAEvC,MAAM,YAAY,GAChB,CAAC,MAAM,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC,IAAI,EAAE,CAAC;IAEjF,MAAM,eAAe,GAAG,KAAK,EAAE,UAA6C,EAAE,EAAE;QAC9E,MAAM,YAAY,GAAgB,EAAE,CAAC;QACrC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,yBAAyB,CAAC,KAAK,EAAE;gBACtD,GAAG,KAAK;gBACR,YAAY,EAAE,UAAU;aACzB,CAAC,CAAC;YACH,YAAY,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEF,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QACzB,MAAM,UAAU,GAAG;YACjB,GAAG,KAAK,CAAC,YAAY;SACtB,CAAC;QAEF,KAAK,CAAC,QAAQ,GAAG,MAAM,eAAe,CAAC,UAAU,CAAC,CAAC;QAEnD,OAAO,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAED,MAAM,qBAAqB,GAAG,CAAC,KAAa,EAAE,MAAW,EAAE,EAAE;QAC3D,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC1E,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;gBACf,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC,IAAI,GAAG,EAAE,kBAAkB,CAAC,CAAC;YACtF,CAAC;iBAAM,CAAC;gBACN,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACtE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CACvC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;QAChC,MAAM,UAAU,GAAG;YACjB,GAAG,KAAK,CAAC,YAAY;YACrB,GAAG,MAAM;SACV,CAAC;QAEF,MAAM,eAAe,GAAG,MAAM,eAAe,CAAC,UAAU,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,qBAAqB,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC/D,MAAM,mBAAmB,GAAG,qBAAqB,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAE5E,OAAO;YACL,GAAG,KAAK;YACR,iCAAiC;YACjC,UAAU,EAAE,mBAAmB;YAC/B,+CAA+C;YAC/C,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,WAAW;YAClB,QAAQ,EAAE,eAAe;SAC1B,CAAC;IACJ,CAAC,CAAC,CACH,CAAC;IAEF,OAAO,CAAC,KAAK,EAAE,GAAG,eAAe,CAAC,CAAC;AACrC,CAAC;AAED,oBAAoB;AACpB,SAAS,MAAM,CAAI,KAAU,EAAE,GAAwB;IACrD,MAAM,IAAI,GAA4B,EAAE,CAAC;IACzC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;QAC3B,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,MAAW;IACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,mEAAmE,MAAM,EAAE,CAAC,CAAC;IAC/F,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,QAAkB,EAAE,QAA6B;IACvE,MAAM,KAAK,GAAG;QACZ,GAAG,QAAQ;KACZ,CAAC;IACF,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;QAC5B,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;QACnC,CAAC;IACH,CAAC;IAED,OAAO;QACL,GAAG;QACH,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;aAClB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,MAAM,GAAG,MAAM,KAAK,EAAE,CAAC;aAC7C,IAAI,CAAC,KAAK,CAAC;QACd,GAAG;KACJ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACf,CAAC;AAED,SAAgB,kBAAkB,CAChC,KAAgD,EAChD,MAAyC;IAEzC,gBAAgB;IAChB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;IACD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE;QAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACnC,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC;IAC/C,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACnD,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAM,IAAI,KAAK,CACb,IACE,KAAK,CAAC,UACR,wFAAwF,MAAM,wCAAwC,MAAM,KAAK,QAAQ;aACtJ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;aACpB,IAAI,CAAC,IAAI,CAAC,iBAAiB,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,CACjE,CAAC;IACJ,CAAC;IAED,MAAM,mBAAmB,GAAG,CAC1B,OAA0B,EAC1B,KAAU,EACV,qBAA+B,EAC/B,EAAE;QACF,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CACb,qCAAqC,KAAK,CAAC,UAAU,qBACnD,OAAO,CAAC,IACV,0CAA0C,OAAO,KAAK,oBAAoB,KAAK,IAAI,CACpF,CAAC;QACJ,CAAC;QACD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CACb,qCAAqC,KAAK,CAAC,UAAU,qBAAqB,OAAO,CAAC,IAAI,2DAA2D,KAAK,IAAI,CAC3J,CAAC;QACJ,CAAC;QACD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CACb,qCAAqC,KAAK,CAAC,UAAU,qBAAqB,OAAO,CAAC,IAAI,oCAAoC,KAAK,IAAI,CACpI,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEF,wDAAwD;IACxD,KAAK,MAAM,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7B,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,CAAC;YACD,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,mBAAmB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["import type { DynamicConvention, RouteNode } from './Route';\n\nexport async function loadStaticParamsAsync(route: RouteNode): Promise<RouteNode> {\n  const expandedChildren = await Promise.all(\n    route.children.map((route) => loadStaticParamsRecursive(route, { parentParams: {} }))\n  );\n  route.children = expandedChildren.flat();\n  return route;\n}\n\nexport async function evalStaticParamsAsync(\n  route: RouteNode,\n  props: { parentParams: any },\n  generateStaticParams?: (props: {\n    params?: Record<string, string | string[]>;\n  }) => Record<string, string | string[]>[]\n): Promise<Record<string, string | string[]>[] | null> {\n  if (!route.dynamic && generateStaticParams) {\n    throw new Error(\n      'Cannot use generateStaticParams in a route without dynamic segments: ' + route.contextKey\n    );\n  }\n\n  if (generateStaticParams) {\n    const staticParams = await generateStaticParams({\n      params: props.parentParams || {},\n    });\n\n    assertStaticParamsType(staticParams);\n    // Assert that at least one param from each matches the dynamic route.\n    staticParams.forEach((params) => assertStaticParams(route, params));\n\n    return staticParams;\n  }\n  return null;\n}\n\nasync function loadStaticParamsRecursive(\n  route: RouteNode,\n  props: { parentParams: any }\n): Promise<RouteNode[]> {\n  if (!route?.dynamic && !route?.children?.length) {\n    return [route];\n  }\n\n  const loaded = await route.loadRoute();\n\n  const staticParams =\n    (await evalStaticParamsAsync(route, props, loaded.generateStaticParams)) ?? [];\n\n  const traverseForNode = async (nextParams: Record<string, string | string[]>) => {\n    const nextChildren: RouteNode[] = [];\n    for (const child of route.children) {\n      const children = await loadStaticParamsRecursive(child, {\n        ...props,\n        parentParams: nextParams,\n      });\n      nextChildren.push(...children);\n    }\n\n    return uniqBy(nextChildren, (i) => i.route);\n  };\n\n  if (!staticParams.length) {\n    const nextParams = {\n      ...props.parentParams,\n    };\n\n    route.children = await traverseForNode(nextParams);\n\n    return [route];\n  }\n\n  const createParsedRouteName = (input: string, params: any) => {\n    let parsedRouteName = input;\n    route.dynamic?.map((query) => {\n      const param = params[query.name];\n      const formattedParameter = Array.isArray(param) ? param.join('/') : param;\n      if (query.deep) {\n        parsedRouteName = parsedRouteName.replace(`[...${query.name}]`, formattedParameter);\n      } else {\n        parsedRouteName = parsedRouteName.replace(`[${query.name}]`, param);\n      }\n    });\n\n    return parsedRouteName;\n  };\n\n  const generatedRoutes = await Promise.all(\n    staticParams.map(async (params) => {\n      const nextParams = {\n        ...props.parentParams,\n        ...params,\n      };\n\n      const dynamicChildren = await traverseForNode(nextParams);\n      const parsedRoute = createParsedRouteName(route.route, params);\n      const generatedContextKey = createParsedRouteName(route.contextKey, params);\n\n      return {\n        ...route,\n        // TODO: Add a new field for this\n        contextKey: generatedContextKey,\n        // Convert the dynamic route to a static route.\n        dynamic: null,\n        route: parsedRoute,\n        children: dynamicChildren,\n      };\n    })\n  );\n\n  return [route, ...generatedRoutes];\n}\n\n/** lodash.uniqBy */\nfunction uniqBy<T>(array: T[], key: (item: T) => string): T[] {\n  const seen: Record<string, boolean> = {};\n  return array.filter((item) => {\n    const k = key(item);\n    if (seen[k]) {\n      return false;\n    }\n    seen[k] = true;\n    return true;\n  });\n}\n\nfunction assertStaticParamsType(params: any): asserts params is Record<string, string | string[]> {\n  if (!Array.isArray(params)) {\n    throw new Error(`generateStaticParams() must return an array of params, received ${params}`);\n  }\n}\n\nfunction formatExpected(expected: string[], received: Record<string, any>): string {\n  const total = {\n    ...received,\n  };\n  for (const item of expected) {\n    if (total[item] == null) {\n      total[item] = String(total[item]);\n    } else {\n      total[item] = `\"${total[item]}\"`;\n    }\n  }\n\n  return [\n    '{',\n    Object.entries(total)\n      .map(([key, value]) => `  \"${key}\": ${value}`)\n      .join(',\\n'),\n    '}',\n  ].join('\\n');\n}\n\nexport function assertStaticParams(\n  route: Pick<RouteNode, 'contextKey' | 'dynamic'>,\n  params: Record<string, string | string[]>\n) {\n  // Type checking\n  if (!route.dynamic) {\n    throw new Error('assertStaticParams() must be called on a dynamic route.');\n  }\n  const matches = route.dynamic.every((dynamic) => {\n    const value = params[dynamic.name];\n    return value !== undefined && value !== null;\n  });\n  if (!matches) {\n    const plural = route.dynamic.length > 1 ? 's' : '';\n    const expected = route.dynamic.map((dynamic) => dynamic.name);\n    throw new Error(\n      `[${\n        route.contextKey\n      }]: generateStaticParams() must return an array of params that match the dynamic route${plural}. Expected non-nullish values for key${plural}: ${expected\n        .map((v) => `\"${v}\"`)\n        .join(', ')}.\\nReceived:\\n${formatExpected(expected, params)}`\n    );\n  }\n\n  const validateSingleParam = (\n    dynamic: DynamicConvention,\n    value: any,\n    allowMultipleSegments?: boolean\n  ) => {\n    if (typeof value !== 'string') {\n      throw new Error(\n        `generateStaticParams() for route \"${route.contextKey}\" expected param \"${\n          dynamic.name\n        }\" to be of type string, instead found \"${typeof value}\" while parsing \"${value}\".`\n      );\n    }\n    const parts = value.split('/').filter(Boolean);\n    if (parts.length > 1 && !allowMultipleSegments) {\n      throw new Error(\n        `generateStaticParams() for route \"${route.contextKey}\" expected param \"${dynamic.name}\" to not contain \"/\" (multiple segments) while parsing \"${value}\".`\n      );\n    }\n    if (parts.length === 0) {\n      throw new Error(\n        `generateStaticParams() for route \"${route.contextKey}\" expected param \"${dynamic.name}\" not to be empty while parsing \"${value}\".`\n      );\n    }\n  };\n\n  // `[shape]/bar/[...colors]` -> `[shape]`, `[...colors]`\n  for (const dynamic of route.dynamic) {\n    let parameter = params[dynamic.name];\n    if (dynamic.deep) {\n      if (Array.isArray(parameter)) {\n        parameter = parameter.filter(Boolean).join('/');\n      }\n      validateSingleParam(dynamic, parameter, true);\n    } else {\n      validateSingleParam(dynamic, parameter);\n    }\n  }\n}\n"]}