{"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["../../src/utils/url.ts"], "names": [], "mappings": ";;AAIA,oDAEC;AAED,wCAKC;AAED,oDAGC;AAlBD;;;GAGG;AACH,SAAgB,oBAAoB,CAAC,IAAY;IAC/C,OAAO,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAED,SAAgB,cAAc,CAAC,IAAY;IACzC,mHAAmH;IACnH,8HAA8H;IAC9H,mGAAmG;IACnG,OAAO,yEAAyE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9F,CAAC;AAED,SAAgB,oBAAoB,CAAC,IAAY;IAC/C,uEAAuE;IACvE,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AACvF,CAAC", "sourcesContent": ["/**\n * Does the input string start with a valid URL scheme.\n * NOTE: Additional strictness added to ensure URLs sent in query parameters for in-app navigation are not matched.\n */\nexport function hasUrlProtocolPrefix(href: string): boolean {\n  return /^([\\w\\d_+.-]+:)?\\/\\//.test(href);\n}\n\nexport function isWellKnownUri(href: string): boolean {\n  // This is a hack and we should change this to work like the web in the future where we have full confidence in the\n  // ability to match URLs and send anything unmatched to the OS. The main difference between this and `hasUrlProtocolPrefix` is\n  // that we don't require `//`, e.g. `mailto:` is valid and common, and `mailto://bacon` is invalid.\n  return /^(https?|mailto|tel|sms|geo|maps|market|itmss?|itms-apps|content|file):/.test(href);\n}\n\nexport function shouldLinkExternally(href: string): boolean {\n  // Cheap check first to avoid regex if the href is not a path fragment.\n  return !href.startsWith('.') && (hasUrlProtocolPrefix(href) || isWellKnownUri(href));\n}\n"]}