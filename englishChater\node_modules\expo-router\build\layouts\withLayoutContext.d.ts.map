{"version": 3, "file": "withLayoutContext.d.ts", "sourceRoot": "", "sources": ["../../src/layouts/withLayoutContext.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AACzE,OAAc,EAGZ,cAAc,EACd,aAAa,EACb,yBAAyB,EACzB,eAAe,EACf,SAAS,EACT,aAAa,EAEd,MAAM,OAAO,CAAC;AAGf,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,EAAoB,WAAW,EAAE,MAAM,eAAe,CAAC;AAC9D,OAAO,EAA2B,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAGxE,wBAAgB,uBAAuB,CACrC,QAAQ,EAAE,SAAS,EACnB,EACE,iBAAiB,EACjB,UAAU,GACX,GAAE;IACD,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,uCAAuC;IACvC,UAAU,CAAC,EAAE,MAAM,CAAC;CAChB;;cAKkC,MAAM;;;;EAuD/C;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,wBAAgB,iBAAiB,CAC/B,QAAQ,SAAS,MAAM,EACvB,CAAC,SAAS,aAAa,CAAC,GAAG,CAAC,EAC5B,MAAM,SAAS,eAAe,EAC9B,SAAS,SAAS,YAAY,EAC9B,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,KAAK,WAAW,EAAE,GAwBxD,yBAAyB,CAC5B,eAAe,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CACrF,GAAG;IACF,MAAM,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC,KAAK,IAAI,CAAC;IAClE,SAAS,EAAE,OAAO,SAAS,CAAC;CAC7B,CACF"}