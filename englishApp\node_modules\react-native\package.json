{"name": "react-native", "version": "0.79.2", "description": "A framework for building native apps using React", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/react-native"}, "homepage": "https://reactnative.dev/", "keywords": ["react", "react-native", "android", "ios", "mobile", "cross-platform", "app-framework", "mobile-development"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "bin": {"react-native": "cli.js"}, "types": "types", "jest-junit": {"outputDirectory": "reports/junit", "outputName": "js-test-results.xml"}, "files": ["android", "build.gradle.kts", "cli.js", "flow", "gradle.properties", "gradle/libs.versions.toml", "index.js", "interface.js", "jest-preset.js", "jest", "Libraries", "LICENSE", "React-Core.podspec", "react-native.config.js", "React.podspec", "React", "!React/Fabric/RCTThirdPartyFabricComponentsProvider.*", "ReactAndroid", "ReactApple", "ReactCommon", "README.md", "rn-get-polyfills.js", "scripts/compose-source-maps.js", "scripts/find-node-for-xcode.sh", "scripts/bundle.js", "scripts/generate-codegen-artifacts.js", "scripts/generate-provider-cli.js", "scripts/generate-specs-cli.js", "scripts/codegen", "!scripts/codegen/__tests__", "!scripts/codegen/__test_fixtures__", "scripts/hermes/hermes-utils.js", "scripts/hermes/prepare-hermes-for-build.js", "scripts/ios-configure-glog.sh", "scripts/xcode/ccache-clang++.sh", "scripts/xcode/ccache-clang.sh", "scripts/xcode/ccache.conf", "scripts/xcode/with-environment.sh", "scripts/native_modules.rb", "scripts/node-binary.sh", "scripts/packager.sh", "scripts/packager-reporter.js", "scripts/react_native_pods_utils/script_phases.rb", "scripts/react_native_pods_utils/script_phases.sh", "scripts/react_native_pods.rb", "scripts/cocoapods", "!scripts/cocoapods/__tests__", "scripts/react-native-xcode.sh", "sdks/.hermesversion", "sdks/hermes-engine", "sdks/hermesc", "settings.gradle.kts", "src", "template.config.js", "template", "!template/node_modules", "!template/package-lock.json", "!template/yarn.lock", "third-party-podspecs", "types"], "scripts": {"prepack": "node ./scripts/prepack.js", "featureflags": "node ./scripts/featureflags/index.js"}, "peerDependencies": {"@types/react": "^19.0.0", "react": "^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "dependencies": {"@jest/create-cache-key-function": "^29.7.0", "@react-native/assets-registry": "0.79.2", "@react-native/codegen": "0.79.2", "@react-native/community-cli-plugin": "0.79.2", "@react-native/gradle-plugin": "0.79.2", "@react-native/js-polyfills": "0.79.2", "@react-native/normalize-colors": "0.79.2", "@react-native/virtualized-lists": "0.79.2", "abort-controller": "^3.0.0", "anser": "^1.4.9", "ansi-regex": "^5.0.0", "babel-jest": "^29.7.0", "babel-plugin-syntax-hermes-parser": "0.25.1", "base64-js": "^1.5.1", "chalk": "^4.0.0", "commander": "^12.0.0", "event-target-shim": "^5.0.1", "flow-enums-runtime": "^0.0.6", "glob": "^7.1.1", "invariant": "^2.2.4", "jest-environment-node": "^29.7.0", "memoize-one": "^5.0.0", "metro-runtime": "^0.82.0", "metro-source-map": "^0.82.0", "nullthrows": "^1.1.1", "pretty-format": "^29.7.0", "promise": "^8.3.0", "react-devtools-core": "^6.1.1", "react-refresh": "^0.14.0", "regenerator-runtime": "^0.13.2", "scheduler": "0.25.0", "semver": "^7.1.3", "stacktrace-parser": "^0.1.10", "whatwg-fetch": "^3.0.0", "ws": "^6.2.3", "yargs": "^17.6.2"}, "codegenConfig": {"libraries": [{"name": "FBReactNativeSpec", "type": "modules", "ios": {}, "android": {}, "jsSrcsDir": "src"}, {"name": "rncore", "type": "components", "ios": {}, "android": {}, "jsSrcsDir": "src"}]}}