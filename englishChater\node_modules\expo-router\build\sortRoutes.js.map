{"version": 3, "file": "sortRoutes.js", "sourceRoot": "", "sources": ["../src/sortRoutes.ts"], "names": [], "mappings": ";;AAaA,gCAgDC;AAED,sDAYC;AA1ED,yCAA4C;AAE5C,SAAS,qBAAqB,CAAC,CAAoB,EAAE,CAAoB;IACvE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACtB,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;QACtB,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAgB,UAAU,CAAC,CAAY,EAAE,CAAY;IACnD,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC5B,OAAO,CAAC,CAAC;IACX,CAAC;IACD,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;QAC5B,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1C,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;QAC7C,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC3C,MAAM,CAAC,GAAG,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACpD,IAAI,CAAC,EAAE,CAAC;oBACN,OAAO,CAAC,CAAC;gBACX,CAAC;YACH,CAAC;YACD,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC5C,OAAO,CAAC,CAAC;YACX,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC5C,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YAED,MAAM,CAAC,GAAG,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,EAAE,CAAC;gBACN,OAAO,CAAC,CAAC;YACX,CAAC;QACH,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,KAAK,OAAO,IAAI,IAAA,yBAAc,EAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IACtE,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,KAAK,OAAO,IAAI,IAAA,yBAAc,EAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IAEtE,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QACtB,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;QACtB,OAAO,CAAC,CAAC;IACX,CAAC;IAED,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;AACzC,CAAC;AAED,SAAgB,qBAAqB,CAAC,gBAAyB;IAC7D,OAAO,CAAC,CAAY,EAAE,CAAY,EAAU,EAAE;QAC5C,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,CAAC,KAAK,KAAK,gBAAgB,EAAE,CAAC;gBACjC,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,CAAC,KAAK,KAAK,gBAAgB,EAAE,CAAC;gBACjC,OAAO,CAAC,CAAC;YACX,CAAC;QACH,CAAC;QACD,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { DynamicConvention, RouteNode } from './Route';\nimport { matchGroupName } from './matchers';\n\nfunction sortDynamicConvention(a: DynamicConvention, b: DynamicConvention) {\n  if (a.deep && !b.deep) {\n    return 1;\n  }\n  if (!a.deep && b.deep) {\n    return -1;\n  }\n  return 0;\n}\n\nexport function sortRoutes(a: RouteNode, b: RouteNode): number {\n  if (a.dynamic && !b.dynamic) {\n    return 1;\n  }\n  if (!a.dynamic && b.dynamic) {\n    return -1;\n  }\n  if (a.dynamic && b.dynamic) {\n    if (a.dynamic.length !== b.dynamic.length) {\n      return b.dynamic.length - a.dynamic.length;\n    }\n\n    for (let i = 0; i < a.dynamic.length; i++) {\n      const aDynamic = a.dynamic[i];\n      const bDynamic = b.dynamic[i];\n\n      if (aDynamic.notFound && bDynamic.notFound) {\n        const s = sortDynamicConvention(aDynamic, bDynamic);\n        if (s) {\n          return s;\n        }\n      }\n      if (aDynamic.notFound && !bDynamic.notFound) {\n        return 1;\n      }\n      if (!aDynamic.notFound && bDynamic.notFound) {\n        return -1;\n      }\n\n      const s = sortDynamicConvention(aDynamic, bDynamic);\n      if (s) {\n        return s;\n      }\n    }\n    return 0;\n  }\n\n  const aIndex = a.route === 'index' || matchGroupName(a.route) != null;\n  const bIndex = b.route === 'index' || matchGroupName(b.route) != null;\n\n  if (aIndex && !bIndex) {\n    return -1;\n  }\n  if (!aIndex && bIndex) {\n    return 1;\n  }\n\n  return a.route.length - b.route.length;\n}\n\nexport function sortRoutesWithInitial(initialRouteName?: string) {\n  return (a: RouteNode, b: RouteNode): number => {\n    if (initialRouteName) {\n      if (a.route === initialRouteName) {\n        return -1;\n      }\n      if (b.route === initialRouteName) {\n        return 1;\n      }\n    }\n    return sortRoutes(a, b);\n  };\n}\n"]}