{"version": 3, "file": "imperative-api.d.ts", "sourceRoot": "", "sources": ["../src/imperative-api.tsx"], "names": [], "mappings": "AAEA,OAAO,EAQL,iBAAiB,EAOlB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AAExD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,MAAM,MAAM,GAAG;IACnB;;OAEG;IACH,IAAI,EAAE,MAAM,IAAI,CAAC;IACjB;;OAEG;IACH,SAAS,EAAE,MAAM,OAAO,CAAC;IACzB;;OAEG;IACH,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,iBAAiB,KAAK,IAAI,CAAC;IACxD;;OAEG;IACH,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC5D;;;;;;SAMK;IACL,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC3D;;;;OAIG;IACH,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC;IAClC;;OAEG;IACH,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC7D;;;OAGG;IACH,UAAU,EAAE,MAAM,IAAI,CAAC;IACvB;;;;OAIG;IACH,UAAU,EAAE,MAAM,OAAO,CAAC;IAC1B;;OAEG;IACH,SAAS,EAAE,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAC3E;;;OAGG;IACH,MAAM,EAAE,MAAM,IAAI,CAAC;IACnB;;OAEG;IACH,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;CAChC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,MAAM,EAAE,MAapB,CAAC;AAEF,wBAAgB,oBAAoB,SAUnC"}