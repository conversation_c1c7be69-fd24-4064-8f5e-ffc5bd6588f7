{"version": 3, "file": "entry.js", "sourceRoot": "", "sources": ["../../src/rsc/entry.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,YAAY,CAAC;AAPb;;;;;GAKG;;;;;AAgDH,kBAQC;AApDD,kDAA0B;AAC1B,mFAAkE;AAElE,4CAAyC;AACzC,0DAAuD;AACvD,sCAAuD;AAEvD,2BAA2B;AAC3B,SAAS,iBAAiB,CAAC,KAAyB;IAClD,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,SAAS,YAAY;YACnB,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAChB,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;QACD,uDAAuD;QACvD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,UAAU,CAAC,6BAA6B,KAAK,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,OAAO,CAC5D,UAAU,CAAC,sBAAuB,CACnC,CAAC;YACF,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACjB,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YAC1E,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,6BAA6B,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACjE,CAAC;YACD,UAAU,CAAC,sBAAsB,GAAG,YAAY,CAAC;QACnD,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAE/B,OAAO,CACL,CAAC,6BAAa,CACZ,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CACnB,KAAK,CAAC,CAAC,GAAG,EAAE;YACV,mEAAmE;YACnE,0DAA0D;YAC1D,UAAU,CAAC,+BAA+B,EAAE,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,EACF,CACH,CAAC;AACJ,CAAC;AAED,4DAA4D;AAC5D,SAAgB,GAAG;IACjB,OAAO,CACL,CAAC,iDAAgB,CACf;MAAA,CAAC,SAAG,CAAC,KAAK,CAAC,CAAC,iBAAiB,CAAC,CAC5B;QAAA,CAAC,eAAM,CAAC,AAAD,EACT;MAAA,EAAE,SAAG,CACP;IAAA,EAAE,iDAAgB,CAAC,CACpB,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use client';\n\nimport React from 'react';\nimport { SafeAreaProvider } from 'react-native-safe-area-context';\n\nimport { Router } from './router/client';\nimport { ErrorBoundary } from '../views/ErrorBoundary';\nimport { ErrorBoundaryProps, Try } from '../views/Try';\n\n// Add root error recovery.\nfunction RootErrorBoundary(props: ErrorBoundaryProps) {\n  React.useEffect(() => {\n    function refetchRoute() {\n      if (props.error) {\n        props.retry();\n      }\n    }\n    // TODO: Only strip when not connected to a dev server.\n    if (process.env.NODE_ENV === 'development') {\n      globalThis.__EXPO_RSC_RELOAD_LISTENERS__ ||= [];\n      const index = globalThis.__EXPO_RSC_RELOAD_LISTENERS__.indexOf(\n        globalThis.__EXPO_REFETCH_ROUTE__!\n      );\n      if (index !== -1) {\n        globalThis.__EXPO_RSC_RELOAD_LISTENERS__.splice(index, 1, refetchRoute);\n      } else {\n        globalThis.__EXPO_RSC_RELOAD_LISTENERS__.unshift(refetchRoute);\n      }\n      globalThis.__EXPO_REFETCH_ROUTE__ = refetchRoute;\n    }\n  }, [props.error, props.retry]);\n\n  return (\n    <ErrorBoundary\n      error={props.error}\n      retry={() => {\n        // TODO: Invalidate the cache automatically when the request fails.\n        // Invalidate the fetch cache so we can retry the request.\n        globalThis.__EXPO_REFETCH_ROUTE_NO_CACHE__?.();\n        return props.retry();\n      }}\n    />\n  );\n}\n\n// Must be exported or Fast Refresh won't update the context\nexport function App() {\n  return (\n    <SafeAreaProvider>\n      <Try catch={RootErrorBoundary}>\n        <Router />\n      </Try>\n    </SafeAreaProvider>\n  );\n}\n"]}