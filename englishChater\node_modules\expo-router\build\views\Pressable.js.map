{"version": 3, "file": "Pressable.js", "sourceRoot": "", "sources": ["../../src/views/Pressable.tsx"], "names": [], "mappings": ";;;AACA,+CAKsB;AA4BT,QAAA,SAAS,GAAG,wBAAgD,CAAC", "sourcesContent": ["import { ClassAttributes, ComponentProps, ComponentType } from 'react';\nimport {\n  Pressable as NativePressable,\n  StyleProp,\n  ViewStyle,\n  PressableStateCallbackType as NativePressableStateCallbackType,\n} from 'react-native';\n\ntype NativePressableProps = ComponentProps<typeof NativePressable> &\n  ClassAttributes<typeof NativePressable>;\n\nexport type PressableStateCallbackType = NativePressableStateCallbackType & {\n  readonly pressed: boolean;\n  readonly hovered: boolean;\n  /** @platform web */\n  readonly focused: boolean;\n};\n\nexport type WebPressableProps = {\n  /**\n   * Either children or a render prop that receives a boolean reflecting whether\n   * the component is currently pressed.\n   */\n  children?: React.ReactNode | ((state: PressableStateCallbackType) => React.ReactNode);\n\n  /**\n   * Either view styles or a function that receives a boolean reflecting whether\n   * the component is currently pressed and returns view styles.\n   */\n  style?: StyleProp<ViewStyle> | ((state: PressableStateCallbackType) => StyleProp<ViewStyle>);\n};\n\nexport type PressableProps = Omit<NativePressableProps, 'children' | 'style'> & WebPressableProps;\n\nexport const Pressable = NativePressable as ComponentType<PressableProps>;\n"]}