{"version": 3, "file": "DrawerClient.js", "sourceRoot": "", "sources": ["../../src/layouts/DrawerClient.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AAEb,qDAIkC;AAGlC,2DAAwD;AAExD,MAAM,eAAe,GAAG,IAAA,8BAAqB,GAAE,CAAC,SAAS,CAAC;AAE7C,QAAA,MAAM,GAAG,IAAA,qCAAiB,EAKrC,eAAe,CAAC,CAAC;AAEnB,kBAAe,cAAM,CAAC", "sourcesContent": ["'use client';\n\nimport {\n  createDrawerNavigator,\n  DrawerNavigationOptions,\n  DrawerNavigationEventMap,\n} from '@react-navigation/drawer';\nimport { DrawerNavigationState, ParamListBase } from '@react-navigation/native';\n\nimport { withLayoutContext } from './withLayoutContext';\n\nconst DrawerNavigator = createDrawerNavigator().Navigator;\n\nexport const Drawer = withLayoutContext<\n  DrawerNavigationOptions,\n  typeof DrawerNavigator,\n  DrawerNavigationState<ParamListBase>,\n  DrawerNavigationEventMap\n>(DrawerNavigator);\n\nexport default Drawer;\n"]}