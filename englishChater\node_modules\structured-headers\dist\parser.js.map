{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../src/parser.ts"], "names": [], "mappings": ";;;AAAA,mCAQiB;AAEjB,mCAAgC;AAEhC,iCAAiC;AAEjC,SAAgB,eAAe,CAAC,KAAa;IAE3C,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,eAAe,EAAE,CAAC;AAElC,CAAC;AALD,0CAKC;AAED,SAAgB,SAAS,CAAC,KAAa;IAErC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;AAE5B,CAAC;AALD,8BAKC;AAED,SAAgB,SAAS,CAAC,KAAa;IAErC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;AAE5B,CAAC;AALD,8BAKC;AAED,MAAa,UAAW,SAAQ,KAAK;IAEnC,YAAY,QAAgB,EAAE,OAAc;QAE1C,KAAK,CAAC,gBAAgB,OAAO,cAAc,QAAQ,EAAE,CAAC,CAAC;IAEzD,CAAC;CAEF;AARD,gCAQC;AAED,MAAqB,MAAM;IAKzB,YAAY,KAAa;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAED,eAAe;QAEb,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YAEjB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAG,GAAG,EAAE;gBACzB,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,MAAM,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;aACtC;iBAAM;gBACL,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;aACzC;YACD,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACd,OAAO,UAAU,CAAC;aACnB;YACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACd,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,uCAAuC,CAAC,CAAC;aACzE;SACF;QACD,OAAO,UAAU,CAAC;IAEpB,CAAC;IAED,SAAS;QAEP,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,OAAO,GAAS,EAAE,CAAC;QACzB,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACjB,OAAO,CAAC,IAAI,CACV,IAAI,CAAC,oBAAoB,EAAE,CAC5B,CAAC;YACF,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACd,OAAO,OAAO,CAAC;aAChB;YACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBACd,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,0CAA0C,CAAC,CAAC;aAC5E;SACF;QAED,OAAO,OAAO,CAAC;IAEjB,CAAC;IAED,SAAS,CAAC,iBAA0B,IAAI;QAEtC,IAAI,cAAc;YAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QAElC,MAAM,MAAM,GAAS;YACnB,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,eAAe,EAAE;SACvB,CAAC;QAEF,IAAI,cAAc;YAAE,IAAI,CAAC,UAAU,EAAE,CAAC;QACtC,OAAO,MAAM,CAAC;IAEhB,CAAC;IAEO,oBAAoB;QAE1B,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAG,GAAG,EAAE;YACzB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;SAC9B;aAAM;YACL,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC9B;IAEH,CAAC;IAEO,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,MAAM,SAAS,GAAW,EAAE,CAAC;QAE7B,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACjB,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE;gBAC3B,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,OAAO;oBACL,SAAS;oBACT,IAAI,CAAC,eAAe,EAAE;iBACvB,CAAC;aACH;YAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAEtC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjC,IAAI,QAAQ,KAAG,GAAG,IAAI,QAAQ,KAAK,GAAG,EAAE;gBACtC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,8DAA8D,CAAC,CAAC;aAChG;SACF;QAGD,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,kCAAkC,CAAC,CAAC;IAErE,CAAC;IAEO,aAAa;QAEnB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;SACrC;QACD,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;SAC3B;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;SAC1B;QACD,IAAI,IAAI,KAAK,GAAG,EAAG;YACjB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;QACD,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;SAC5B;QAED,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;IAErD,CAAC;IAEO,eAAe;QAErB,MAAM,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACjB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,IAAI,KAAG,GAAG,EAAE;gBACd,MAAM;aACP;YACD,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,IAAI,KAAK,GAAa,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE;gBAC3B,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;aAC9B;YACD,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;SAC5B;QAED,OAAO,UAAU,CAAC;IAEpB,CAAC;IAEO,qBAAqB;QAE3B,IAAI,IAAI,GAA0B,SAAS,CAAC;QAC5C,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAG,GAAG,EAAE;YACzB,IAAI,GAAG,CAAC,CAAC,CAAC;YACV,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;QAED,uDAAuD;QACvD,mBAAmB;QACnB,oDAAoD;QACpD,GAAG;QAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE;YAC7B,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;SAC1D;QAED,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACjB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;gBACjB,WAAW,IAAE,IAAI,CAAC;aACnB;iBAAM,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,GAAG,EAAE;gBAC7C,IAAI,WAAW,CAAC,MAAM,GAAC,EAAE,EAAE;oBACzB,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC;iBACnE;gBACD,WAAW,IAAE,GAAG,CAAC;gBACjB,IAAI,GAAG,SAAS,CAAC;aAClB;iBAAM;gBACL,4DAA4D;gBAC5D,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,MAAM;aACP;YAED,IAAI,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,MAAM,GAAC,EAAE,EAAE;gBAC/C,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC;aACnE;YACD,IAAI,IAAI,KAAK,SAAS,IAAI,WAAW,CAAC,MAAM,GAAC,EAAE,EAAE;gBAC/C,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC;aACnE;SACF;QAED,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,OAAO,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;SACzC;aAAM;YACL,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC7B,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,gCAAgC,CAAC,CAAC;aAClE;YACD,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAC,CAAC,EAAE;gBACtC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,0DAA0D,CAAC,CAAC;aAC5F;YACD,OAAO,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;SACvC;IAEH,CAAC;IAEO,WAAW;QAEjB,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACjB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,IAAI,KAAG,IAAI,EAAE;gBACf,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;oBACd,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC;iBAC3D;gBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChC,IAAI,QAAQ,KAAG,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE;oBACvC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,mEAAmE,CAAC,CAAC;iBACrG;gBACD,YAAY,IAAE,QAAQ,CAAC;aACxB;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACvB,OAAO,YAAY,CAAC;aACrB;iBAAM,IAAI,CAAC,cAAO,CAAC,IAAI,CAAC,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;aACvD;iBAAM;gBACL,YAAY,IAAI,IAAI,CAAC;aACtB;SAEF;QACD,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC;IAE5D,CAAC;IAEO,UAAU;QAEhB,0EAA0E;QAC1E,6CAA6C;QAC7C,+FAA+F;QAC/F,GAAG;QAEH,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACjB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACjD,OAAO,IAAI,aAAK,CAAC,YAAY,CAAC,CAAC;aAChC;YACD,YAAY,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,aAAK,CAAC,YAAY,CAAC,CAAC;IAEjC,CAAC;IAEO,iBAAiB;QAEvB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;YACjB,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,qEAAqE,CAAC,CAAC;SACvG;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,GAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACzC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,qDAAqD,CAAC,CAAC;SACvF;QAED,OAAO,IAAI,oBAAY,CAAC,UAAU,CAAC,CAAC;IAEtC,CAAC;IAEO,YAAY;QAElB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,GAAG,EAAE,CAAC;QAEX,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,OAAO,KAAK,CAAC;SACd;QACD,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,+CAA+C,CAAC,CAAC;IAElF,CAAC;IAEO,QAAQ;QAEd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YACrC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,mDAAmD,CAAC,CAAC;SACrF;QAED,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,OAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACjB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACjC,OAAO,YAAY,CAAC;aACrB;YACD,YAAY,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,YAAY,CAAC;IAEtB,CAAC;IAED;;OAEG;IACK,QAAQ;QAEd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE9B,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,IAAY;QAE7B,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAG,IAAI,EAAE;YAC1B,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC;SACpD;IAEH,CAAC;IAEO,OAAO;QAEb,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAEhC,CAAC;IACO,GAAG;QAET,OAAO,IAAI,CAAC,GAAG,IAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAErC,CAAC;IACD,+CAA+C;IACvC,OAAO;QAEb,OAAO,IAAI,EAAE;YACX,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE;gBAC3B,IAAI,CAAC,GAAG,EAAE,CAAC;aACZ;iBAAM;gBACL,MAAM;aACP;SACF;IAEH,CAAC;IACD,0CAA0C;IAClC,MAAM;QAEZ,OAAM,IAAI,CAAC,QAAQ,EAAE,KAAG,GAAG,EAAE;YAC3B,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;IAEH,CAAC;IAED,2EAA2E;IAC3E,4BAA4B;IACpB,UAAU;QAEhB,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;YACf,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,uCAAuC,CAAC,CAAC;SACzE;IAEH,CAAC;CAEF;AAtYD,yBAsYC;AAED,MAAM,YAAY,GAAG,SAAS,CAAC;AAC/B,SAAS,OAAO,CAAC,IAAY;IAE3B,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEjC,CAAC"}