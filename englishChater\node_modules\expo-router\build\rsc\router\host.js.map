{"version": 3, "file": "host.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/host.tsx"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;AAEH,uCAAuC;AACvC,YAAY,CAAC;AAXb;;;;;;;;GAQG;;;;;;AAKH,oEAAuC;AACvC,iCASe;AAEf,6EAAyD;AAEzD,qCAA8D;AAC9D,mCAAgC;AAChC,mCAAsD;AACtD,qDAAkD;AAClD,wCAAwD;AAYxD,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,gBAAU,CAAC;AAEpD,sDAAsD;AACtD,MAAM,MAAM;AACV,mDAAmD;AACnD,OAAO,kBAAkB,KAAK,WAAW,CAAC;AAE5C,mCAAmC;AACnC,MAAM,QAAQ,GAAG,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,6BAA6B;AAEjF,+GAA+G;AAC/G,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;AAEzD,IAAI,SAAS,GAAG,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC;AAEzC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;IAC/B,SAAS,GAAG,GAAG,GAAG,SAAS,CAAC;AAC9B,CAAC;AAED,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;IAC7B,SAAS,IAAI,GAAG,CAAC;AACnB,CAAC;AAED,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;IACtB,MAAM,IAAI,KAAK,CACb,8BAA8B,SAAS,oFACrC,IAAA,2BAAY,GAAE,CAAC,aACjB,EAAE,CACH,CAAC;AACJ,CAAC;AAED,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5D,0DAA0D;IAC1D,MAAM,QAAQ,GAAG,wBAAS,CAAC,UAAU,CAAC;IAEtC,MAAM,mBAAmB,GACvB,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC;IAE9E,kJAAkJ;IAClJ,IAAI,mBAAmB,KAAK,KAAK,EAAE,CAAC;QAClC,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC;QAEjF,IAAI,QAAQ,EAAE,CAAC;YACb,0EAA0E;YAC1E,MAAM,IAAI,KAAK,CACb,6JAA6J,CAC9J,CAAC;QACJ,CAAC;QAED,sEAAsE;QACtE,MAAM,IAAI,KAAK,CACb,yPAAyP,CAC1P,CAAC;IACJ,CAAC;IAED,8EAA8E;IAC9E,8HAA8H;IAC9H,MAAM,IAAI,KAAK,CACb,sOAAsO,CACvO,CAAC;AACJ,CAAC;AAKD,MAAM,gBAAgB,GAAG,kBAAkB,CAAC;AAE5C,MAAM,KAAK,GAAG,GAAG,CAAC;AAClB,MAAM,YAAY,GAAG,GAAG,CAAC;AACzB,MAAM,aAAa,GAAG,GAAG,CAAC;AAQ1B,MAAM,iBAAiB,GAAe,EAAE,CAAC;AAEzC,MAAM,gBAAgB,GACpB,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK;IAC3B,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,0EAA0E;QAC1E;YACE,eAAe,EAAE,UAAU;YAC3B,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,GAAG;SACb,CAAC;AAER,MAAM,cAAc,GAAG;IACrB,GAAG,gBAAgB;IACnB,MAAM,EAAE,gBAAgB;IACxB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,OAAQ;CACtC,CAAC;AAWF,MAAM,WAAW,GAAG,KAAK,EAA0B,eAA2B,EAAc,EAAE;IAC5F,8CAA8C;IAC9C,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC;IACvC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,qGAAqG;QACrG,yEAAyE;QACzE,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,EAAE,CAAC;YACpE,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxC,IAAI,SAAc,CAAC;YACnB,IAAI,CAAC;gBACH,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACpC,CAAC;YAAC,MAAM,CAAC;gBACP,+KAA+K;gBAC/K,IAAI,SAAS,CAAC,UAAU,CAAC,0BAA0B,CAAC,EAAE,CAAC;oBACrD,OAAO,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;oBAC/D,gIAAgI;oBAChI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;gBAC7B,CAAC;gBACD,MAAM,IAAI,yBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;YACzF,CAAC;YAED,MAAM,IAAI,yBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,YAAoB,CAAC;QACzB,IAAI,CAAC;YACH,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,yBAAgB,CACxB,QAAQ,CAAC,UAAU,EACnB,QAAQ,CAAC,GAAG,EACZ,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,OAAO,CACjB,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,yBAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC5F,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAKF,SAAS,SAAS,CAAI,CAAU,EAAE,CAAqB,EAAE,CAAS;IAChE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,CAAC;AACpD,CAAC;AAED,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC7B,MAAM,aAAa,GAAG,CAAC,CAAW,EAAE,CAAW,EAAY,EAAE;IAC3D,MAAM,SAAS,GAAG,GAAG,EAAE;QACrB,MAAM,OAAO,GAAa,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACxD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBAChB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;gBACf,MAAM,YAAY,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;gBACpC,OAAO,YAAY,CAAC,MAAM,CAAC;gBAC3B,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;gBACjB,OAAO,CAAC,YAAY,CAAC,CAAC;YACxB,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;gBACX,CAAC,CAAC,IAAI,CACJ,CAAC,CAAC,EAAE,EAAE;oBACJ,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;oBACjB,MAAM,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,EACD,GAAG,EAAE;oBACH,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;oBACtB,MAAM,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IACF,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC,IAAI,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACzD,OAAO,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF;;;GAGG;AACI,MAAM,aAAa,GAAG,KAAK,EAChC,QAAgB,EAChB,IAAgB,EAChB,UAAU,GAAG,iBAAiB,EAC9B,EAAE;IACF,MAAM,GAAG,GAAG,yBAAyB,CAAC,SAAS,GAAG,IAAA,mBAAW,EAAC,IAAA,sBAAc,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzF,MAAM,QAAQ,GACZ,IAAI,KAAK,SAAS;QAChB,CAAC,CAAC,IAAA,aAAK,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;QACzC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAC9B,IAAA,aAAK,EAAC,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAC9D,CAAC;IACR,MAAM,IAAI,GAAG,eAAe,CAAoB,WAAW,CAAC,QAAQ,CAAC,EAAE;QACrE,UAAU,EAAE,CAAC,QAAgB,EAAE,IAAe,EAAE,EAAE,CAAC,IAAA,qBAAa,EAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC;KAC7F,CAAC,CAAC;IACH,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClC,IAAA,uBAAe,EAAC,GAAG,EAAE;QACnB,oDAAoD;QACpD,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC;AAC7B,CAAC,CAAC;AArBW,QAAA,aAAa,iBAqBxB;AAEF,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAA6B,CAAC;AAElE,MAAM,gBAAgB,GAAG,CAAC,GAAW,EAAE,MAAe,EAAE,EAAE,CACxD,MAAM,KAAK,SAAS;IAClB,CAAC,CAAC,IAAA,aAAK,EAAC,GAAG,EAAE;QACT,kBAAkB;QAClB,OAAO,EAAE;YACP,GAAG,gBAAgB;YACnB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,OAAQ;SACtC;KACF,CAAC;IACJ,CAAC,CAAC,OAAO,MAAM,KAAK,QAAQ;QAC1B,CAAC,CAAC,IAAA,aAAK,EAAC,GAAG,EAAE;YACT,OAAO,EAAE;gBACP,GAAG,gBAAgB;gBACnB,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,OAAQ;gBACrC,eAAe,EAAE,MAAM;aACxB;SACF,CAAC;QACJ,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAChC,IAAA,aAAK,EAAC,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAC9D,CAAC;AAEH,MAAM,QAAQ,GAAG,CACtB,KAAa,EACb,MAAgB,EAChB,UAAU,GAAG,iBAAiB,EACpB,EAAE;IACZ,mCAAmC;IACnC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAG,GAAG,EAAE;YACtB,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;YACzB,MAAM,IAAI,GAAG,IAAA,gBAAQ,EAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YACjD,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,UAAU,CAAC,6BAA6B,KAAK,EAAE,CAAC;QAChD,MAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,OAAO,CAC5D,UAAU,CAAC,oBAAqB,CACjC,CAAC;QACF,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,6BAA6B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC;QACD,UAAU,CAAC,oBAAoB,GAAG,UAAU,CAAC;IAC/C,CAAC;IAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;QACvD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,2CAA2C;IAC3C,MAAM,UAAU,GAAG,CAAE,UAAkB,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;IACpE,oDAAoD;IACpD,0GAA0G;IAC1G,MAAM,GAAG,GAAG,yBAAyB,CAAC,SAAS,GAAG,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC,CAAC;IACtE,MAAM,0BAA0B,GAC9B,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;QACjB,2CAA2C;QAC3C,uDAAuD;QACvD,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;IAC/F,MAAM,QAAQ,GAAG,0BAA0B,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAC9F,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;IACvB,MAAM,IAAI,GAAG,eAAe,CAAoB,WAAW,CAAC,QAAQ,CAAC,EAAE;QACrE,UAAU,EAAE,CAAC,QAAgB,EAAE,IAAe,EAAE,EAAE,CAAC,IAAA,qBAAa,EAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC;KAC7F,CAAC,CAAC;IACH,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClC,mEAAmE;IACnE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC1C,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAhDW,QAAA,QAAQ,YAgDnB;AAEF,SAAS,yBAAyB,CAAC,IAAY;IAC7C,IAAI,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QACpD,MAAM,MAAM,GAAG,IAAA,4BAAsB,GAAE,CAAC;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CACb,+GAA+G,CAChH,CAAC;QACJ,CAAC;QACD,4EAA4E;QAC5E,OAAO,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC1C,CAAC;IAED,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;AACxD,CAAC;AAEM,MAAM,WAAW,GAAG,CAAC,KAAa,EAAE,MAAgB,EAAQ,EAAE;IACnE,2CAA2C;IAC3C,MAAM,UAAU,GAAG,CAAE,UAAkB,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;IACpE,MAAM,GAAG,GAAG,yBAAyB,CAAC,SAAS,GAAG,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC;QACzB,UAAU,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAChD,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;AACH,CAAC,CAAC;AARW,QAAA,WAAW,eAQtB;AAEF,MAAM,cAAc,GAAG,IAAA,qBAAa,EAElC,GAAG,EAAE;IACL,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC5C,CAAC,CAAC,CAAC;AACH,MAAM,eAAe,GAAG,IAAA,qBAAa,EAAkB,IAAI,CAAC,CAAC;AAEtD,MAAM,IAAI,GAAG,CAAC,EACnB,YAAY,EACZ,aAAa,EACb,UAAU,GAAG,iBAAiB,EAE9B,oBAAoB,EACpB,QAAQ,GAOT,EAAE,EAAE;IACH,UAAU,CAAC,aAAa,CAAC,GAAG,oBAAoB,CAAC;IACjD,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,IAAA,gBAAQ,EAAC,GAAG,EAAE,CAC5C,IAAA,gBAAQ,EAAC,YAAY,IAAI,EAAE,EAAE,aAAa,EAAE,UAAU,CAAC,CACxD,CAAC;IACF,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,UAAU,CAAC,YAAY,CAAC,GAAG,WAAW,CAAC;IACzC,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IAC9B,MAAM,OAAO,GAAG,IAAA,mBAAW,EACzB,CAAC,KAAa,EAAE,MAAgB,EAAE,EAAE;QAClC,oCAAoC;QACpC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;QACzB,MAAM,IAAI,GAAG,IAAA,gBAAQ,EAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QACjD,IAAA,uBAAe,EAAC,GAAG,EAAE;YACnB,WAAW,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,EACD,CAAC,UAAU,CAAC,CACb,CAAC;IACF,OAAO,IAAA,qBAAa,EAClB,cAAc,CAAC,QAAQ,EACvB,EAAE,KAAK,EAAE,OAAO,EAAE,EAClB,IAAA,qBAAa,EAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CACvE,CAAC;AACJ,CAAC,CAAC;AArCW,QAAA,IAAI,QAqCf;AAEK,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC,IAAA,WAAG,EAAC,cAAc,CAAC,CAAC;AAAvC,QAAA,UAAU,cAA6B;AAEpD,MAAM,eAAe,GAAG,IAAA,qBAAa,EAAY,SAAS,CAAC,CAAC;AAC5D,MAAM,uBAAuB,GAAG,IAAA,YAAI,EAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;AAExD,MAAM,IAAI,GAAG,CAAC,EACnB,EAAE,EACF,QAAQ,EACR,QAAQ,GAKT,EAAE,EAAE;IACH,MAAM,eAAe,GAAG,IAAA,WAAG,EAAC,eAAe,CAAC,CAAC;IAC7C,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC5C,CAAC;IACD,MAAM,QAAQ,GAAG,IAAA,WAAG,EAAC,eAAe,CAAC,CAAC;IACtC,IAAI,CAAC,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,CAAC;QACtB,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,aAAa,GAAG,EAAE,GAAG,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1F,CAAC;IACD,OAAO,IAAA,qBAAa,EAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF,CAAC,CAAC;AArBW,QAAA,IAAI,QAqBf;AAEK,MAAM,QAAQ,GAAG,GAAG,EAAE,CAAC,IAAA,WAAG,EAAC,eAAe,CAAC,CAAC;AAAtC,QAAA,QAAQ,YAA8B;AAEnD;;;GAGG;AACI,MAAM,UAAU,GAAG,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAA+C,EAAE,EAAE,CAChG,IAAA,qBAAa,EAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;AAD5D,QAAA,UAAU,cACkD", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * https://github.com/dai-shi/waku/blob/32d52242c1450b5f5965860e671ff73c42da8bd0/packages/waku/src/client.ts#L1\n */\n\n//// <reference types=\"react/canary\" />\n'use client';\n\nimport Constants from 'expo-constants';\nimport {\n  createContext,\n  createElement,\n  memo,\n  useCallback,\n  useState,\n  startTransition,\n  use,\n  useEffect,\n} from 'react';\nimport type { ReactNode } from 'react';\nimport RSDWClient from 'react-server-dom-webpack/client';\n\nimport { MetroServerError, ReactServerError } from './errors';\nimport { fetch } from './fetch';\nimport { encodeInput, encodeActionId } from './utils';\nimport { getDevServer } from '../../getDevServer';\nimport { getOriginFromConstants } from '../../head/url';\n\ndeclare namespace globalThis {\n  let __EXPO_RSC_RELOAD_LISTENERS__: undefined | (() => void)[];\n  let __EXPO_REFETCH_RSC__: undefined | (() => void);\n  let expo:\n    | undefined\n    | {\n        modules?: { ExpoGo?: unknown };\n      };\n}\n\nconst { createFromFetch, encodeReply } = RSDWClient;\n\n// TODO: Maybe this could be a bundler global instead.\nconst IS_DOM =\n  // @ts-expect-error: Added via react-native-webview\n  typeof ReactNativeWebView !== 'undefined';\n\n// NOTE: Ensured to start with `/`.\nconst RSC_PATH = '/_flight/' + process.env.EXPO_OS; // process.env.EXPO_RSC_PATH;\n\n// Using base URL for remote hosts isn't currently supported in DOM components as we use it for offline assets.\nconst BASE_URL = IS_DOM ? '' : process.env.EXPO_BASE_URL;\n\nlet BASE_PATH = `${BASE_URL}${RSC_PATH}`;\n\nif (!BASE_PATH.startsWith('/')) {\n  BASE_PATH = '/' + BASE_PATH;\n}\n\nif (!BASE_PATH.endsWith('/')) {\n  BASE_PATH += '/';\n}\n\nif (BASE_PATH === '/') {\n  throw new Error(\n    `Invalid React Flight path \"${BASE_PATH}\". The path should not live at the project root, e.g. /_flight/. Dev server URL: ${\n      getDevServer().fullBundleUrl\n    }`\n  );\n}\n\nif (process.env.EXPO_OS !== 'web' && !window.location?.href) {\n  // This will require a rebuild in bare-workflow to update.\n  const manifest = Constants.expoConfig;\n\n  const originFromConstants =\n    manifest?.extra?.router?.origin ?? manifest?.extra?.router?.generatedOrigin;\n\n  // In legacy cases, this can be extraneously set to false since it was the default before we had a production hosting solution for native servers.\n  if (originFromConstants === false) {\n    const isExpoGo = typeof expo !== 'undefined' && globalThis.expo?.modules?.ExpoGo;\n\n    if (isExpoGo) {\n      // Updating is a bit easier in Expo Go as you don't need a native rebuild.\n      throw new Error(\n        'The \"origin\" property in the app config (app.json) cannot be false when React Server Components is enabled. https://docs.expo.dev/guides/server-components/'\n      );\n    }\n\n    // Add more context about updating the app.json in development builds.\n    throw new Error(\n      'The \"origin\" property in the app config (app.json) cannot be \"false\" when React Server Components is enabled. Remove the \"origin\" property from your Expo config and rebuild the native app to resolve. https://docs.expo.dev/guides/server-components/'\n    );\n  }\n\n  // This can happen if the user attempts to use React Server Components without\n  // enabling the flags in the app.json. This will set origin to false and prevent the expo/metro-runtime polyfill from running.\n  throw new Error(\n    'window.location.href is not defined. This is required for React Server Components to work correctly. Ensure React Server Components is correctly enabled in your project and config. https://docs.expo.dev/guides/server-components/'\n  );\n}\n\ntype OnFetchData = (data: unknown) => void;\ntype SetElements = (updater: Elements | ((prev: Elements) => Elements)) => void;\n\nconst RSC_CONTENT_TYPE = 'text/x-component';\n\nconst ENTRY = 'e';\nconst SET_ELEMENTS = 's';\nconst ON_FETCH_DATA = 'o';\n\ntype FetchCache = {\n  [ENTRY]?: [input: string, params: unknown, elements: Elements];\n  [SET_ELEMENTS]?: SetElements;\n  [ON_FETCH_DATA]?: OnFetchData | undefined;\n};\n\nconst defaultFetchCache: FetchCache = {};\n\nconst NO_CACHE_HEADERS: Record<string, string> =\n  process.env.EXPO_OS === 'web'\n    ? {}\n    : // These are needed for iOS + Prod to get updates after the first request.\n      {\n        'Cache-Control': 'no-cache',\n        Pragma: 'no-cache',\n        Expires: '0',\n      };\n\nconst ACTION_HEADERS = {\n  ...NO_CACHE_HEADERS,\n  accept: RSC_CONTENT_TYPE,\n  'expo-platform': process.env.EXPO_OS!,\n};\n\ninterface ResponseLike {\n  url: string;\n  ok: boolean;\n  status: number;\n  statusText: string;\n  headers: Headers;\n  text(): Promise<string>;\n}\n\nconst checkStatus = async <T extends ResponseLike>(responsePromise: Promise<T>): Promise<T> => {\n  // TODO: Combine with metro async fetch logic.\n  const response = await responsePromise;\n  if (!response.ok) {\n    // NOTE(EvanBacon): Transform the Metro development error into a JS error that can be used by LogBox.\n    // This was tested against using a Class component in a server component.\n    if (__DEV__ && (response.status === 500 || response.status === 404)) {\n      const errorText = await response.text();\n      let errorJson: any;\n      try {\n        errorJson = JSON.parse(errorText);\n      } catch {\n        // `Unable to resolve module` error should respond as JSON from the dev server and sent to the master red box, this can get corrupt when it's returned as the formatted string.\n        if (errorText.startsWith('Unable to resolve module')) {\n          console.error('Unexpected Metro error format from dev server');\n          // This is an unexpected state that occurs when the dev server renderer does not throw Metro errors in the expected JSON format.\n          throw new Error(errorJson);\n        }\n        throw new ReactServerError(errorText, response.url, response.status, response.headers);\n      }\n\n      throw new MetroServerError(errorJson, response.url);\n    }\n\n    let responseText: string;\n    try {\n      responseText = await response.text();\n    } catch {\n      throw new ReactServerError(\n        response.statusText,\n        response.url,\n        response.status,\n        response.headers\n      );\n    }\n    throw new ReactServerError(responseText, response.url, response.status, response.headers);\n  }\n  return response;\n};\n\ntype Elements = Promise<Record<string, ReactNode>> & {\n  prev?: Record<string, ReactNode> | undefined;\n};\nfunction getCached<T>(c: () => T, m: WeakMap<object, T>, k: object): T {\n  return (m.has(k) ? m : m.set(k, c())).get(k) as T;\n}\n\nconst cache1 = new WeakMap();\nconst mergeElements = (a: Elements, b: Elements): Elements => {\n  const getResult = () => {\n    const promise: Elements = new Promise((resolve, reject) => {\n      Promise.all([a, b])\n        .then(([a, b]) => {\n          const nextElements = { ...a, ...b };\n          delete nextElements._value;\n          promise.prev = a;\n          resolve(nextElements);\n        })\n        .catch((e) => {\n          a.then(\n            (a) => {\n              promise.prev = a;\n              reject(e);\n            },\n            () => {\n              promise.prev = a.prev;\n              reject(e);\n            }\n          );\n        });\n    });\n    return promise;\n  };\n  const cache2 = getCached(() => new WeakMap(), cache1, a);\n  return getCached(getResult, cache2, b);\n};\n\n/**\n * callServer callback\n * This is not a public API.\n */\nexport const callServerRSC = async (\n  actionId: string,\n  args?: unknown[],\n  fetchCache = defaultFetchCache\n) => {\n  const url = getAdjustedRemoteFilePath(BASE_PATH + encodeInput(encodeActionId(actionId)));\n  const response =\n    args === undefined\n      ? fetch(url, { headers: ACTION_HEADERS })\n      : encodeReply(args).then((body) =>\n          fetch(url, { method: 'POST', body, headers: ACTION_HEADERS })\n        );\n  const data = createFromFetch<Awaited<Elements>>(checkStatus(response), {\n    callServer: (actionId: string, args: unknown[]) => callServerRSC(actionId, args, fetchCache),\n  });\n  fetchCache[ON_FETCH_DATA]?.(data);\n  startTransition(() => {\n    // FIXME this causes rerenders even if data is empty\n    fetchCache[SET_ELEMENTS]?.((prev) => mergeElements(prev, data));\n  });\n  return (await data)._value;\n};\n\nconst prefetchedParams = new WeakMap<Promise<unknown>, unknown>();\n\nconst fetchRSCInternal = (url: string, params: unknown) =>\n  params === undefined\n    ? fetch(url, {\n        // Disable caching\n        headers: {\n          ...NO_CACHE_HEADERS,\n          'expo-platform': process.env.EXPO_OS!,\n        },\n      })\n    : typeof params === 'string'\n      ? fetch(url, {\n          headers: {\n            ...NO_CACHE_HEADERS,\n            'expo-platform': process.env.EXPO_OS!,\n            'X-Expo-Params': params,\n          },\n        })\n      : encodeReply(params).then((body) =>\n          fetch(url, { method: 'POST', headers: ACTION_HEADERS, body })\n        );\n\nexport const fetchRSC = (\n  input: string,\n  params?: unknown,\n  fetchCache = defaultFetchCache\n): Elements => {\n  // TODO: strip when \"is exporting\".\n  if (process.env.NODE_ENV === 'development') {\n    const refetchRsc = () => {\n      delete fetchCache[ENTRY];\n      const data = fetchRSC(input, params, fetchCache);\n      fetchCache[SET_ELEMENTS]?.(() => data);\n    };\n    globalThis.__EXPO_RSC_RELOAD_LISTENERS__ ||= [];\n    const index = globalThis.__EXPO_RSC_RELOAD_LISTENERS__.indexOf(\n      globalThis.__EXPO_REFETCH_RSC__!\n    );\n    if (index !== -1) {\n      globalThis.__EXPO_RSC_RELOAD_LISTENERS__.splice(index, 1, refetchRsc);\n    } else {\n      globalThis.__EXPO_RSC_RELOAD_LISTENERS__.push(refetchRsc);\n    }\n    globalThis.__EXPO_REFETCH_RSC__ = refetchRsc;\n  }\n\n  const entry = fetchCache[ENTRY];\n  if (entry && entry[0] === input && entry[1] === params) {\n    return entry[2];\n  }\n\n  // eslint-disable-next-line no-multi-assign\n  const prefetched = ((globalThis as any).__EXPO_PREFETCHED__ ||= {});\n  // TODO: Load from on-disk on native when indicated.\n  // const reqPath = fetchOptions?.remote ? getAdjustedRemoteFilePath(url) : getAdjustedRemoteFilePath(url);\n  const url = getAdjustedRemoteFilePath(BASE_PATH + encodeInput(input));\n  const hasValidPrefetchedResponse =\n    !!prefetched[url] &&\n    // HACK .has() is for the initial hydration\n    // It's limited and may result in a wrong result. FIXME\n    (!prefetchedParams.has(prefetched[url]) || prefetchedParams.get(prefetched[url]) === params);\n  const response = hasValidPrefetchedResponse ? prefetched[url] : fetchRSCInternal(url, params);\n  delete prefetched[url];\n  const data = createFromFetch<Awaited<Elements>>(checkStatus(response), {\n    callServer: (actionId: string, args: unknown[]) => callServerRSC(actionId, args, fetchCache),\n  });\n  fetchCache[ON_FETCH_DATA]?.(data);\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  fetchCache[ENTRY] = [input, params, data];\n  return data;\n};\n\nfunction getAdjustedRemoteFilePath(path: string): string {\n  if (IS_DOM && process.env.NODE_ENV === 'production') {\n    const origin = getOriginFromConstants();\n    if (!origin) {\n      throw new Error(\n        'Expo RSC: Origin not found in Constants. This is required for production DOM components using server actions.'\n      );\n    }\n    // DOM components in production need to use the same origin logic as native.\n    return new URL(path, origin).toString();\n  }\n\n  if (!IS_DOM && process.env.EXPO_OS === 'web') {\n    return path;\n  }\n\n  return new URL(path, window.location.href).toString();\n}\n\nexport const prefetchRSC = (input: string, params?: unknown): void => {\n  // eslint-disable-next-line no-multi-assign\n  const prefetched = ((globalThis as any).__EXPO_PREFETCHED__ ||= {});\n  const url = getAdjustedRemoteFilePath(BASE_PATH + encodeInput(input));\n  if (!(url in prefetched)) {\n    prefetched[url] = fetchRSCInternal(url, params);\n    prefetchedParams.set(prefetched[url], params);\n  }\n};\n\nconst RefetchContext = createContext<\n  (input: string, searchParams?: URLSearchParams | string) => void\n>(() => {\n  throw new Error('Missing Root component');\n});\nconst ElementsContext = createContext<Elements | null>(null);\n\nexport const Root = ({\n  initialInput,\n  initialParams,\n  fetchCache = defaultFetchCache,\n\n  unstable_onFetchData,\n  children,\n}: {\n  initialInput?: string;\n  initialParams?: unknown;\n  fetchCache?: FetchCache;\n  unstable_onFetchData?: (data: unknown) => void;\n  children: ReactNode;\n}) => {\n  fetchCache[ON_FETCH_DATA] = unstable_onFetchData;\n  const [elements, setElements] = useState(() =>\n    fetchRSC(initialInput || '', initialParams, fetchCache)\n  );\n  useEffect(() => {\n    fetchCache[SET_ELEMENTS] = setElements;\n  }, [fetchCache, setElements]);\n  const refetch = useCallback(\n    (input: string, params?: unknown) => {\n      // clear cache entry before fetching\n      delete fetchCache[ENTRY];\n      const data = fetchRSC(input, params, fetchCache);\n      startTransition(() => {\n        setElements((prev) => mergeElements(prev, data));\n      });\n    },\n    [fetchCache]\n  );\n  return createElement(\n    RefetchContext.Provider,\n    { value: refetch },\n    createElement(ElementsContext.Provider, { value: elements }, children)\n  );\n};\n\nexport const useRefetch = () => use(RefetchContext);\n\nconst ChildrenContext = createContext<ReactNode>(undefined);\nconst ChildrenContextProvider = memo(ChildrenContext.Provider);\n\nexport const Slot = ({\n  id,\n  children,\n  fallback,\n}: {\n  id: string;\n  children?: ReactNode;\n  fallback?: ReactNode;\n}) => {\n  const elementsPromise = use(ElementsContext);\n  if (!elementsPromise) {\n    throw new Error('Missing Root component');\n  }\n  const elements = use(elementsPromise);\n  if (!(id in elements)) {\n    if (fallback) {\n      return fallback;\n    }\n    throw new Error('Not found: ' + id + '. Expected: ' + Object.keys(elements).join(', '));\n  }\n  return createElement(ChildrenContextProvider, { value: children }, elements[id]);\n};\n\nexport const Children = () => use(ChildrenContext);\n\n/**\n * ServerRoot for SSR\n * This is not a public API.\n */\nexport const ServerRoot = ({ elements, children }: { elements: Elements; children: ReactNode }) =>\n  createElement(ElementsContext.Provider, { value: elements }, children);\n"]}