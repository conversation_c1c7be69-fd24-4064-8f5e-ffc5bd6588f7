{"version": 3, "file": "Screen.js", "sourceRoot": "", "sources": ["../../src/views/Screen.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBb,wBAeC;AAED,4BAgCC;AArED,+CAAuE;AAEvE,oDAAiD;AAejD,MAAM,eAAe,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,eAAK,CAAC,eAAe,CAAC,CAAC,CAAC,cAAa,CAAC,CAAC;AAE/F,sEAAsE;AACtE,SAAgB,MAAM,CAAmC,EAAE,IAAI,EAAE,OAAO,EAAyB;IAC/F,MAAM,UAAU,GAAG,IAAA,6BAAa,EAAC,IAAI,CAAC,CAAC;IAEvC,eAAe,CAAC,GAAG,EAAE;QACnB,IACE,OAAO;YACP,kGAAkG;YAClG,4CAA4C;YAC5C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAC3B,CAAC;YACD,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;IAE1B,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,QAAQ,CACtB,KAAgB,EAChB,UAAmB;IAEnB,IAAI,IAAA,sBAAc,EAAC,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QAC5D,IACE,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;YAC/B,KAAK,CAAC,KAAK;YACX,MAAM,IAAI,KAAK,CAAC,KAAK;YACrB,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EACjB,CAAC;YACD,MAAM,IAAI,KAAK,CACb,sDAAsD,UAAU,8EAA8E,CAC/I,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,IACE,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC,IAAI,CAC5C,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,CAC9E,EACD,CAAC;gBACD,MAAM,IAAI,KAAK,CACb,sDAAsD,UAAU,yHAAyH,CAC1L,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["'use client';\nimport React, { isValidElement, ReactElement, ReactNode } from 'react';\n\nimport { useNavigation } from '../useNavigation';\n\nexport type ScreenProps<TOptions extends Record<string, any> = Record<string, any>> = {\n  /**\n   * Name is required when used inside a Layout component.\n   *\n   * When used in a route, this can be an absolute path like `/(root)` to the parent route or a relative path like `../../` to the parent route.\n   * This should not be used inside of a Layout component.\n   * @example `/(root)` maps to a layout route `/app/(root).tsx`.\n   */\n  name?: string;\n  initialParams?: Record<string, any>;\n  options?: TOptions;\n};\n\nconst useLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : function () {};\n\n/** Component for setting the current screen's options dynamically. */\nexport function Screen<TOptions extends object = object>({ name, options }: ScreenProps<TOptions>) {\n  const navigation = useNavigation(name);\n\n  useLayoutEffect(() => {\n    if (\n      options &&\n      // React Navigation will infinitely loop in some cases if an empty object is passed to setOptions.\n      // https://github.com/expo/router/issues/452\n      Object.keys(options).length\n    ) {\n      navigation.setOptions(options);\n    }\n  }, [navigation, options]);\n\n  return null;\n}\n\nexport function isScreen(\n  child: ReactNode,\n  contextKey?: string\n): child is ReactElement<ScreenProps & { name: string }> {\n  if (isValidElement(child) && child && child.type === Screen) {\n    if (\n      typeof child.props === 'object' &&\n      child.props &&\n      'name' in child.props &&\n      !child.props.name\n    ) {\n      throw new Error(\n        `<Screen /> component in \\`default export\\` at \\`app${contextKey}/_layout\\` must have a \\`name\\` prop when used as a child of a Layout Route.`\n      );\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        ['children', 'component', 'getComponent'].some(\n          (key) => child.props && typeof child.props === 'object' && key in child.props\n        )\n      ) {\n        throw new Error(\n          `<Screen /> component in \\`default export\\` at \\`app${contextKey}/_layout\\` must not have a \\`children\\`, \\`component\\`, or \\`getComponent\\` prop when used as a child of a Layout Route`\n        );\n      }\n    }\n\n    return true;\n  }\n\n  return false;\n}\n"]}