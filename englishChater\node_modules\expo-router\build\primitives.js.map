{"version": 3, "file": "primitives.js", "sourceRoot": "", "sources": ["../src/primitives.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;AAEb,qDAAkE;AAElE,oGAAoG;AACpG,gBAAgB;AACH,KAAoB,IAAA,+BAAsB,EAAC,EAAS,CAAC,EAAE,EAArD,cAAM,cAAE,aAAK,YAAyC", "sourcesContent": ["'use client';\n\nimport { createNavigatorFactory } from '@react-navigation/native';\n\n// `@react-navigation/native` does not expose the Screen or Group components directly, so we have to\n// do this hack.\nexport const { Screen, Group } = createNavigatorFactory({} as any)();\n"]}