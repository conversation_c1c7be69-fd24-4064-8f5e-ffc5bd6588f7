{"version": 3, "file": "context-stubs.js", "sourceRoot": "", "sources": ["../../src/testing-library/context-stubs.ts"], "names": [], "mappings": ";;;;;;AAsBA,0CAoBC;AAED,kEAkBC;AA9DD,gDAAwB;AAExB,0FAAwD;AAgB/C,yBAhBF,kCAAc,CAgBE;AAEvB,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAEvD,SAAgB,eAAe,CAAC,OAAsB;IACpD,OAAO,MAAM,CAAC,MAAM,CAClB,UAAU,EAAU;QAClB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACnD,OAAO,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACpF,CAAC,EACD;QACE,OAAO,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG;QAC7B,EAAE,EAAE,GAAG;QACP,IAAI,EAAE,GAAG,EAAE,CACT,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YAC/B,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC/B,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC7C,GAAG,GAAG,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;YAExD,OAAO,GAAG,CAAC;QACb,CAAC,CAAC;KACL,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,2BAA2B,CAAC,GAAW,EAAE,SAAwB;IAC/E,MAAM,eAAe,GAAG,IAAA,kCAAc,EAAC,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;IAEzE,OAAO,MAAM,CAAC,MAAM,CAClB,UAAU,EAAU;QAClB,IAAI,EAAE,IAAI,SAAS,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;YAC5B,OAAO,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,OAAO,eAAe,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC,EACD;QACE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC;QAClE,OAAO,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG;QAC7B,EAAE,EAAE,GAAG;KACR,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["import path from 'path';\n\nimport requireContext from './require-context-ponyfill';\nimport { NativeIntent } from '../types';\n\nexport type ReactComponent = () => React.ReactElement<any, any> | null;\nexport type NativeIntentStub = NativeIntent;\nexport type FileStub =\n  | (Record<string, unknown> & {\n      default: ReactComponent;\n      unstable_settings?: Record<string, any>;\n    })\n  | ReactComponent;\n\nexport type MemoryContext = Record<string, FileStub | NativeIntentStub> & {\n  '+native-intent'?: NativeIntentStub;\n};\n\nexport { requireContext };\n\nconst validExtensions = ['.js', '.jsx', '.ts', '.tsx'];\n\nexport function inMemoryContext(context: MemoryContext) {\n  return Object.assign(\n    function (id: string) {\n      id = id.replace(/^\\.\\//, '').replace(/\\.\\w*$/, '');\n      return typeof context[id] === 'function' ? { default: context[id] } : context[id];\n    },\n    {\n      resolve: (key: string) => key,\n      id: '0',\n      keys: () =>\n        Object.keys(context).map((key) => {\n          const ext = path.extname(key);\n          key = key.replace(/^\\.\\//, '');\n          key = key.startsWith('/') ? key : `./${key}`;\n          key = validExtensions.includes(ext) ? key : `${key}.js`;\n\n          return key;\n        }),\n    }\n  );\n}\n\nexport function requireContextWithOverrides(dir: string, overrides: MemoryContext) {\n  const existingContext = requireContext(path.resolve(process.cwd(), dir));\n\n  return Object.assign(\n    function (id: string) {\n      if (id in overrides) {\n        const route = overrides[id];\n        return typeof route === 'function' ? { default: route } : route;\n      } else {\n        return existingContext(id);\n      }\n    },\n    {\n      keys: () => [...Object.keys(overrides), ...existingContext.keys()],\n      resolve: (key: string) => key,\n      id: '0',\n    }\n  );\n}\n"]}