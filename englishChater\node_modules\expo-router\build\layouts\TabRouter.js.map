{"version": 3, "file": "TabRouter.js", "sourceRoot": "", "sources": ["../../src/layouts/TabRouter.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AAMN,MAAM,iBAAiB,GAE1B,CAAC,QAAQ,EAAE,EAAE;IACf,OAAO;QACL,GAAG,QAAQ;QACX,iBAAiB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YAC5C,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5B,iDAAiD;gBACjD,IAAI,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CACxC,KAAK,EACL;oBACE,GAAG,MAAM;oBACT,IAAI,EAAE,SAAS;iBAChB,EACD,OAAO,CACR,CAAC;gBAEF,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrF,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,sEAAsE;gBACtE,kCAAkC;gBAClC,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;oBAC1B,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;oBAE1C,SAAS,GAAG;wBACV,GAAG,SAAS;wBACZ,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,UAAU;wBAC/B,wDAAwD;wBACxD,OAAO,EAAE;4BACP,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC;4BAC5C,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;yBAC7C;qBACF,CAAC;gBACJ,CAAC;gBAED,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO,QAAQ,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AA/CW,QAAA,iBAAiB,qBA+C5B;AAEF,SAAS,eAAe,CACtB,MAAwB;IAExB,OAAO,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC;AACnC,CAAC", "sourcesContent": ["'use client';\nimport { type NavigationAction, type StackActionType } from '@react-navigation/native';\nimport type { ComponentProps } from 'react';\n\nimport type { BottomTabNavigator } from './TabsClient';\n\nexport const tabRouterOverride: NonNullable<\n  ComponentProps<BottomTabNavigator>['UNSTABLE_router']\n> = (original) => {\n  return {\n    ...original,\n    getStateForAction: (state, action, options) => {\n      if (action.target && action.target !== state.key) {\n        return null;\n      }\n\n      if (isReplaceAction(action)) {\n        // Generate the state as if we were using JUMP_TO\n        let nextState = original.getStateForAction(\n          state,\n          {\n            ...action,\n            type: 'JUMP_TO',\n          },\n          options\n        );\n\n        if (!nextState || nextState.index === undefined || !Array.isArray(nextState.history)) {\n          return null;\n        }\n\n        // If the state is valid and we didn't JUMP_TO a single history state,\n        // then remove the previous state.\n        if (nextState.index !== 0) {\n          const previousIndex = nextState.index - 1;\n\n          nextState = {\n            ...nextState,\n            key: `${nextState.key}-replace`,\n            // Omit the previous history entry that we are replacing\n            history: [\n              ...nextState.history.slice(0, previousIndex),\n              ...nextState.history.splice(nextState.index),\n            ],\n          };\n        }\n\n        return nextState;\n      }\n\n      return original.getStateForAction(state, action, options);\n    },\n  };\n};\n\nfunction isReplaceAction(\n  action: NavigationAction\n): action is Extract<StackActionType, { type: 'REPLACE' }> {\n  return action.type === 'REPLACE';\n}\n"]}