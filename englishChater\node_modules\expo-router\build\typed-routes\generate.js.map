{"version": 3, "file": "generate.js", "sourceRoot": "", "sources": ["../../src/typed-routes/generate.ts"], "names": [], "mappings": ";;AAmBA,sEA+GC;AAjID,4CAAyC;AACzC,0CAAwD;AAGxD,oCAAoC;AACpC,MAAM,SAAS,GAAG,gBAAgB,CAAC;AACnC,6BAA6B;AAC7B,MAAM,IAAI,GAAG,UAAU,CAAC;AACxB,2DAA2D;AAC3D,MAAM,KAAK,GAAG,kBAAkB,CAAC;AAEjC,MAAM,SAAS,GAAG,qCAAqC,CAAC;AAOxD,SAAgB,6BAA6B,CAC3C,GAAmB,EACnB,EACE,kBAAkB,GAAG,KAAK,EAC1B,kBAAkB,GAAG,KAAK,MACc,EAAE;IAE5C,IAAI,SAAS,GAAqB,IAAI,CAAC;IAEvC,IAAI,CAAC;QACH,SAAS,GAAG,IAAA,qBAAS,EAAC,GAAG,EAAE;YACzB,MAAM,EAAE,CAAC,mBAAmB,CAAC,EAAE,oBAAoB;YACnD,cAAc,EAAE,KAAK,EAAE,qDAAqD;YAC5E,QAAQ,EAAE,KAAK,EAAE,yCAAyC;YAC1D,iBAAiB,EAAE,IAAI;YACvB,mBAAmB,EAAE,IAAI;YACzB,UAAU,EAAE,OAAO,EAAE,sBAAsB;SAC5C,CAAC,CAAC;IACL,CAAC;IAAC,MAAM,CAAC;QACP,gFAAgF;QAChF,wCAAwC;IAC1C,CAAC;IAED,MAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,mBAAmB,GAAa,CAAC,2BAA2B,EAAE,2BAA2B,CAAC,CAAC;IACjG,MAAM,uBAAuB,GAAa;QACxC,6EAA6E;QAC7E,6EAA6E;KAC9E,CAAC;IACF,MAAM,wBAAwB,GAAa;QACzC,8EAA8E;QAC9E,8EAA8E;KAC/E,CAAC;IAEF,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;QACvC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,SAAS,EAAE,kBAAkB,CAAC,CAAC,CAAC;QACjF,uBAAuB,CAAC,IAAI,CAC1B,eAAe,gBAAgB,CAAC,IAAI,EAAE,kBAAkB,CAAC,yCAAyC,CACnG,CAAC;QACF,wBAAwB,CAAC,IAAI,CAC3B,eAAe,gBAAgB,CAAC,IAAI,EAAE,kBAAkB,CAAC,0CAA0C,CACpG,CAAC;IACJ,CAAC;IAED,MAAM,mBAAmB,GAAa,EAAE,CAAC;IACzC,MAAM,wBAAwB,GAAa,EAAE,CAAC;IAC9C,MAAM,yBAAyB,GAAa,EAAE,CAAC;IAE/C,KAAK,MAAM,CAAC,oBAAoB,EAAE,WAAW,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;QACvE,MAAM,WAAW,GAAG,WAAW;aAC5B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC7D,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,iBAAiB,CAAC;YAClF,OAAO,GAAG,oBAAoB,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC;QACnD,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,MAAM,YAAY,GAAG,WAAW;aAC7B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACb,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC7D,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC9D,OAAO,GAAG,oBAAoB,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC;QACnD,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,mBAAmB,CAAC,IAAI,CACtB,gBAAgB,CACd,oBAAoB;aACjB,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC;aAClC,UAAU,CAAC,IAAI,EAAE,8BAA8B,CAAC,EACnD,kBAAkB,CACnB,CACF,CAAC;QAEF,wBAAwB,CAAC,IAAI,CAC3B,eAAe,gBAAgB,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,2CAA2C,WAAW,MAAM,CACtI,CAAC;QACF,yBAAyB,CAAC,IAAI,CAC5B,eAAe,gBAAgB,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,4CAA4C,YAAY,MAAM,CACxI,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,GAAG;QACX,GAAG,mBAAmB;QACtB,GAAG,uBAAuB;QAC1B,GAAG,mBAAmB;QACtB,GAAG,wBAAwB;KAC5B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEd,MAAM,eAAe,GAAG,CAAC,GAAG,uBAAuB,EAAE,GAAG,wBAAwB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9F,MAAM,gBAAgB,GAAG,CAAC,GAAG,wBAAwB,EAAE,GAAG,yBAAyB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEjG,MAAM,aAAa,GAAG,kBAAkB;QACtC,CAAC,CAAC,qIAAqI;QACvI,CAAC,CAAC,EAAE,CAAC;IAEP,OAAO;;;;;;;;QAQD,aAAa,oBAAoB,eAAe;QAChD,aAAa,qBAAqB,gBAAgB;QAClD,aAAa,SAAS,IAAI;;;;CAIjC,CAAC;AACF,CAAC;AAED,SAAS,eAAe,CACtB,SAA2B,EAC3B,qBAAqB;IACnB,MAAM,EAAE,IAAI,GAAG,EAAU;IACzB,OAAO,EAAE,IAAI,GAAG,EAAoB;CACrC;IAED,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,uBAAuB;IACvB,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC/B,yBAAyB;QACzB,IAAI,SAAS,CAAC,KAAK,KAAK,EAAE,EAAE,CAAC;YAC3B,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACvC,eAAe,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;YAC7C,CAAC;YACD,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,IAAI,QAAgB,CAAC;IAErB,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;QACxB,yEAAyE;QACzE,8EAA8E;QAC9E,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;IAC7B,CAAC;SAAM,CAAC;QACN,QAAQ,GAAG,IAAA,oCAAyB,EAAC,SAAS,CAAC,UAAU,CAAC;aACvD,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,6BAA6B;aACrD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,uBAAuB;IAChD,CAAC;IAED,QAAQ,KAAK,GAAG,CAAC,CAAC,4DAA4D;IAE9E,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC9B,mDAAmD;QACnD,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC5B,CAAC;IAED,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAExC,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAC5B,QAAQ,EACR,QAAQ;aACL,KAAK,CAAC,GAAG,CAAC;aACV,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YAClB,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC1D,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACf,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CACL,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;QACvC,eAAe,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;IAC7C,CAAC;IAED,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED,SAAS,oBAAoB,CAAC,UAAkB;IAC9C,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;AACtF,CAAC;AAED,SAAS,gBAAgB,CAAC,UAAkB,EAAE,kBAA2B;IACvE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;QACrC,OAAO,KAAK,UAAU,IAAI,CAAC;IAC7B,CAAC;IAED,2DAA2D;IAC3D,MAAM,cAAc,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;QAC5D,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,0CAA0C;QAC7E,0EAA0E;QAC1E,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,kBAAkB,EAAE,CAAC;YAC5C,2CAA2C;YAC3C,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC;YACvE,4CAA4C;YAC5C,IAAI,kBAAkB,EAAE,CAAC;gBACvB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YACD,gCAAgC;YAChC,OAAO,MAAM,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC;IAEhE;;;;OAIG;IACH,IAAI,iBAAiB,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAC5C,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;IAC9C,CAAC;IAED,OAAO,KAAK,cAAc,UAAU,iBAAiB,IAAI,CAAC;AAC5D,CAAC", "sourcesContent": ["import { RouteNode } from '../Route';\nimport { getRoutes } from '../getRoutes';\nimport { removeSupportedExtensions } from '../matchers';\nimport { RequireContext } from '../types';\n\n// /[...param1]/ - Match [...param1]\nconst CATCH_ALL = /\\[\\.\\.\\..+?\\]/g;\n// /[param1] - Match [param1]\nconst SLUG = /\\[.+?\\]/g;\n// /(group)/path/(group2)/route - Match [(group), (group2)]\nconst GROUP = /(?:^|\\/)\\(.*?\\)/g;\n\nconst urlParams = \"${`?${string}` | `#${string}` | ''}\";\n\nexport type GetTypedRoutesDeclarationFileOptions = {\n  partialTypedGroups?: boolean;\n  testIgnoreComments?: boolean;\n};\n\nexport function getTypedRoutesDeclarationFile(\n  ctx: RequireContext,\n  {\n    partialTypedGroups = false,\n    testIgnoreComments = false,\n  }: GetTypedRoutesDeclarationFileOptions = {}\n) {\n  let routeNode: RouteNode | null = null;\n\n  try {\n    routeNode = getRoutes(ctx, {\n      ignore: [/_layout\\.[tj]sx?$/], // Skip layout files\n      platformRoutes: false, // We don't need to generate platform specific routes\n      notFound: false, // We don't need +not-found routes either\n      ignoreEntryPoints: true,\n      ignoreRequireErrors: true,\n      importMode: 'async', // Don't load the file\n    });\n  } catch {\n    // Ignore errors from `getRoutes`. This is also called inside the app, which has\n    // a nicer UX for showing error messages\n  }\n\n  const groupedNodes = groupRouteNodes(routeNode);\n  const staticRoutesStrings: string[] = ['Router.RelativePathString', 'Router.ExternalPathString'];\n  const staticRouteInputObjects: string[] = [\n    '{ pathname: Router.RelativePathString, params?: Router.UnknownInputParams }',\n    '{ pathname: Router.ExternalPathString, params?: Router.UnknownInputParams }',\n  ];\n  const staticRouteOutputObjects: string[] = [\n    '{ pathname: Router.RelativePathString, params?: Router.UnknownOutputParams }',\n    '{ pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams }',\n  ];\n\n  for (const type of groupedNodes.static) {\n    staticRoutesStrings.push(contextKeyToType(type + urlParams, partialTypedGroups));\n    staticRouteInputObjects.push(\n      `{ pathname: ${contextKeyToType(type, partialTypedGroups)}; params?: Router.UnknownInputParams; }`\n    );\n    staticRouteOutputObjects.push(\n      `{ pathname: ${contextKeyToType(type, partialTypedGroups)}; params?: Router.UnknownOutputParams; }`\n    );\n  }\n\n  const dynamicRouteStrings: string[] = [];\n  const dynamicRouteInputObjects: string[] = [];\n  const dynamicRouteOutputObjects: string[] = [];\n\n  for (const [dynamicRouteTemplate, paramsNames] of groupedNodes.dynamic) {\n    const inputParams = paramsNames\n      .map((param) => {\n        const key = param.startsWith('...') ? param.slice(3) : param;\n        const value = param.startsWith('...') ? '(string | number)[]' : 'string | number';\n        return `${contextKeyToProperty(key)}: ${value};`;\n      })\n      .join('');\n\n    const outputParams = paramsNames\n      .map((param) => {\n        const key = param.startsWith('...') ? param.slice(3) : param;\n        const value = param.startsWith('...') ? 'string[]' : 'string';\n        return `${contextKeyToProperty(key)}: ${value};`;\n      })\n      .join('');\n\n    dynamicRouteStrings.push(\n      contextKeyToType(\n        dynamicRouteTemplate\n          .replaceAll(CATCH_ALL, '${string}')\n          .replaceAll(SLUG, '${Router.SingleRoutePart<T>}'),\n        partialTypedGroups\n      )\n    );\n\n    dynamicRouteInputObjects.push(\n      `{ pathname: ${contextKeyToType(dynamicRouteTemplate, partialTypedGroups)}, params: Router.UnknownInputParams & { ${inputParams} } }`\n    );\n    dynamicRouteOutputObjects.push(\n      `{ pathname: ${contextKeyToType(dynamicRouteTemplate, partialTypedGroups)}, params: Router.UnknownOutputParams & { ${outputParams} } }`\n    );\n  }\n\n  const href = [\n    ...staticRoutesStrings,\n    ...staticRouteInputObjects,\n    ...dynamicRouteStrings,\n    ...dynamicRouteInputObjects,\n  ].join(' | ');\n\n  const hrefInputParams = [...staticRouteInputObjects, ...dynamicRouteInputObjects].join(' | ');\n  const hrefOutputParams = [...staticRouteOutputObjects, ...dynamicRouteOutputObjects].join(' | ');\n\n  const tsExpectError = testIgnoreComments\n    ? '// @ts-ignore-error -- During tests we need to ignore the \"duplicate\" declaration error, as multiple fixture declare types \\n      '\n    : '';\n\n  return `/* eslint-disable */\nimport * as Router from 'expo-router';\n\nexport * from 'expo-router';\n\ndeclare module 'expo-router' {\n  export namespace ExpoRouter {\n    export interface __routes<T extends string | object = string> {\n      ${tsExpectError}hrefInputParams: ${hrefInputParams};\n      ${tsExpectError}hrefOutputParams: ${hrefOutputParams};\n      ${tsExpectError}href: ${href};\n    }\n  }\n}\n`;\n}\n\nfunction groupRouteNodes(\n  routeNode: RouteNode | null,\n  groupedContextKeys = {\n    static: new Set<string>(),\n    dynamic: new Map<string, string[]>(),\n  }\n) {\n  if (!routeNode) {\n    return groupedContextKeys;\n  }\n\n  // Skip non-route files\n  if (routeNode.type !== 'route') {\n    // Except the root layout\n    if (routeNode.route === '') {\n      for (const child of routeNode.children) {\n        groupRouteNodes(child, groupedContextKeys);\n      }\n      return groupedContextKeys;\n    }\n\n    return groupedContextKeys;\n  }\n\n  let routeKey: string;\n\n  if (routeNode.generated) {\n    // Some routes like the root _layout, _sitemap, +not-found are generated.\n    // We cannot use the contextKey, as their context key does not specify a route\n    routeKey = routeNode.route;\n  } else {\n    routeKey = removeSupportedExtensions(routeNode.contextKey)\n      .replace(/\\/index$/, '') // Remove any trailing /index\n      .replace(/^\\./, ''); // Remove any leading .\n  }\n\n  routeKey ||= '/'; // A routeKey may be empty for contextKey '' or './index.js'\n\n  if (!routeKey.startsWith('/')) {\n    // Not all generated files will have the `/` prefix\n    routeKey = `/${routeKey}`;\n  }\n\n  routeKey = routeKey.replace(/\\\\/g, '/');\n\n  if (routeNode.dynamic) {\n    groupedContextKeys.dynamic.set(\n      routeKey,\n      routeKey\n        .split('/')\n        .filter((segment) => {\n          return segment.startsWith('[') && segment.endsWith(']');\n        })\n        .map((segment) => {\n          return segment.slice(1, -1);\n        })\n    );\n  } else {\n    groupedContextKeys.static.add(routeKey);\n  }\n\n  for (const child of routeNode.children) {\n    groupRouteNodes(child, groupedContextKeys);\n  }\n\n  return groupedContextKeys;\n}\n\nfunction contextKeyToProperty(contextKey: string) {\n  return !/^(?!\\d)[\\w$]+$/.test(contextKey) ? JSON.stringify(contextKey) : contextKey;\n}\n\nfunction contextKeyToType(contextKey: string, partialTypedGroups: boolean) {\n  if (contextKey.match(GROUP) === null) {\n    return `\\`${contextKey}\\``;\n  }\n\n  // If the route has groups, turn them into template strings\n  const typeWithGroups = contextKey.replaceAll(GROUP, (match) => {\n    const groups = match.slice(2, -1); // Remove the leading ( and the trailing )\n    // When `partialRoutes` is enabled, we always change a group to a template\n    if (groups.length > 1 || partialTypedGroups) {\n      // Ensure each group has the trailing slash\n      const groupsAsType = groups.split(',').map((group) => `'/(${group})'`);\n      // `partialRoutes` allow you to skip a group\n      if (partialTypedGroups) {\n        groupsAsType.push(\"''\");\n      }\n      // Combine together into a union\n      return `\\${${groupsAsType.join(' | ')}}`;\n    } else {\n      return match;\n    }\n  });\n\n  let typeWithoutGroups = contextKey.replaceAll(GROUP, '') || '/';\n\n  /**\n   * When getting the static routes, they include a urlParams string at the end.\n   * If we have a route like `/(group)/(group2)`, this would normally be collapsed to `/`.\n   * But because of the urlParams, it becomes `${urlParams}` and we need to add a `/` to the start.\n   */\n  if (typeWithoutGroups.startsWith(urlParams)) {\n    typeWithoutGroups = `/${typeWithoutGroups}`;\n  }\n\n  return `\\`${typeWithGroups}\\` | \\`${typeWithoutGroups}\\``;\n}\n"]}